import style from './index.module.css';
// import Default from './default';
import { useState, useEffect } from 'react';
import Work from './work';
import { useConfigStates } from './utils/configItemStates';
import { getConfig, updateConfig } from '@/api/myqaApi';
import cloneDeep from 'lodash-es/cloneDeep';
import { Spin } from 'antd';
const Home = (): React.ReactElement => {
  const [configs, setConfigs] = useState<any[]>([]);
  const { updateListState: setStateList } = useConfigStates();
  const statusTypeMap = new Map().set(0, '忙碌').set(1, '在线').set(3, '离线');
  const [spin, setSpin] = useState(true);
  useEffect(() => {
    const appChanged = (_event, { type, data }) => {
      const { id, name } = data;
      if (type === 'edit') {
        setConfigs((configs) => {
          return cloneDeep(configs).map((item) => {
            if (item.id === id) {
              item.name = name;
              item.isAccredit = false;
            }
            return item;
          });
        });
        updateConfig(id, name, {
          isAccredit: false
        });
      }
      if (type === 'status') {
        setConfigs((configs) => {
          return cloneDeep(configs).map((item) => {
            if (item.id === id) {
              item.status = statusTypeMap.get(data.status);
            }
            return item;
          });
        });
      }
    };
    getData();
    const remove = window.xBrowser?.on('APP_CHANGED_EVENT', appChanged);
    return () => {
      remove?.();
    };
  }, []);

  const getData = (id?: string) => {
    getConfig()
      .then((res) => {
        const data = res.data.map((item) => {
          const configItem = JSON.parse(localStorage.getItem('configStates') ?? '[]').find(
            (sItem) => item.aiAgentConfigId === sItem.id
          );
          if (!configItem) {
            setStateList('add', { id: item.aiAgentConfigId, state: null });
          }
          return {
            name: item.name,
            id: item.aiAgentConfigId,
            receptionMode: item.receptionMode,
            platformId: item.platformId,
            shopName: item.shopName,
            platformName: item.platformName,
            configContent: item.configContent,
            state: item.aiAgentConfigId === id ? 'ai' : (configItem?.state ?? null),
            status: configItem?.status ? configItem?.status : '',
            // 是否授权
            isAccredit: item.metaData?.isAccredit ?? false
          };
        });
        setConfigs(data);
      })
      .finally(() => {
        setTimeout(() => {
          setSpin(false);
        }, 200);
      });
  };

  const updateConfigState = (id, state) => {
    setConfigs((configs) => {
      return cloneDeep(configs).map((item) => {
        if (item.id === id) {
          item.state = state;
        }
        return item;
      });
    });
  };

  return (
    <div className={style.home}>
      {!spin && (
        <>
          <Work
            getData={getData}
            data={configs.filter((item: any) => !item.isAccredit)}
            updateConfigState={updateConfigState}
          />
        </>
      )}
      {spin && (
        <Spin
          size="large"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}
          spinning={spin}
        />
      )}
    </div>
  );
};

export default Home;

import got from '../../utils/got';

async function updateStatus(ctx, shops, status) {
  const path = `/message/api/clientStatus/sync`;
  let aiStatus;
  let qianNiuStatus;

  if (status.ai === 'connect') {
    aiStatus = AIStatus.RUNNING;
  } else if (status.ai === 'disconnect') {
    aiStatus = AIStatus.STOPPED;
  } else if (status.ai === 'error') {
    aiStatus = AIStatus.ERROR;
  }
  if (status.qianniu === 'running') {
    qianNiuStatus = QianNiuStatus.RUNNING;
  } else if (status.qianniu === 'not-running') {
    qianNiuStatus = QianNiuStatus.STOPPED;
  } else if (status.qianniu === 'unkonwn') {
    qianNiuStatus = QianNiuStatus.UNKNOWN;
  }

  shops.forEach(async (shop) => {
    const res = await got
      .post(ctx.env.host + path, {
        json: {
          aiAssistantRunStatus: aiStatus,
          mallId: shop,
          qianNiuRunStatus: qianNiuStatus,
          clientId: ctx.env.clientId,
          cardId: ctx.env.id
        },
        headers: {
          authorization: ctx.env.accessToken
        }
      })
      .json();
    ctx.log.info('同步客户端状态成功', shop, aiStatus, qianNiuStatus, res);
  });
}

async function updateCSStatus(ctx, shop, status) {
  const path = `/message/api/clientStatus/sync/qianniu/account`;
  let s = 0;
  switch (status) {
    case '在线':
      s = 1;
      break;
    case '挂起':
      s = 2;
      break;
  }

  const res = await got
    .post(ctx.env.host + path, {
      json: {
        accountStatus: s,
        mallId: shop,
        clientId: ctx.env.clientId,
        cardId: ctx.env.id
      },
      headers: {
        authorization: ctx.env.accessToken
      }
    })
    .json();
  ctx.log.info('同步客服状态成功', res);
}

export { updateStatus, updateCSStatus };

enum AIStatus {
  RUNNING = 1,
  STOPPED = 2,
  ERROR = 3
}

enum QianNiuStatus {
  RUNNING = 1,
  STOPPED = 2,
  UNKNOWN = 3
}

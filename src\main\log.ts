import * as log4js from 'log4js';
import os from 'node:os';
import ALY from 'aliyun-sdk';
import OSS from 'ali-oss';
import dayjs from 'dayjs';
import { Lock } from './utils/lock';
import { join } from 'node:path/posix';
import { app } from 'electron';
import { AsyncClient, Content, LogGroup, LogItem, PutLogsRequest } from 'tencentcloud-cls-sdk-js';

const mode = import.meta.env.MODE;
export const clientId = os.userInfo().username + '@' + os.hostname();

const sls = new ALY.SLS({
  accessKeyId: '',
  secretAccessKey: '',
  endpoint: 'http://cn-beijing.log.aliyuncs.com',
  apiVersion: '2015-06-01'
});

// CLS日志服务日志主题ID； 必填参数
const topicID = '1f701eea-6381-4a20-b9b7-a7fc9742739c';
const cls = new AsyncClient({
  // 目标日志主题所在地域域名； 必填参数
  endpoint: 'ap-shanghai.cls.tencentcs.com',
  // 访问密钥ID； 必填参数
  secretId: 'AKID9C3lJfoqgKl02PhngNtGNHzXwAD5nNm2',
  // 访问密钥KEY； 必填参数
  secretKey: 'FLG0a2JROEtOitujNqAIG3f7SYqwrbBZ',
  // 源IP地址： 选填参数， 为空则自动填充本机IP
  sourceIp: '127.0.0.1',
  // 重试次数： 必填参数， 为空则自动填充本机IP
  retry_times: 10
});

const oss = new OSS({
  // yourregion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
  region: 'oss-cn-hangzhou',
  // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
  // 该ak只有写权限
  accessKeyId: 'LTAI5tFBv4yNY7ecjJDi88tg',
  accessKeySecret: '******************************',
  // yourbucketname填写存储空间名称。
  bucket: 'muyan-log'
});

const stdoutAppender = () => ({
  type: 'stdout'
});

const fileAppender = (filename) => ({
  type: 'file',
  filename: filename,
  pattern: 'yyyy-MM-dd',
  compress: true,
  keepFileExt: true,
  numBackups: 15
});

const slsAppender = (topic) => ({
  type: {
    configure: (config, layouts, _findAppender, _levels) => {
      let layout = layouts.basicLayout;
      if (config.layout) {
        layout = layouts.layout(config.layout.type, config.layout);
      }

      return async (loggingEvent) => {
        // console.log('slsAppender', loggingEvent, config, layouts, findAppender, levels);
        sls.putLogs(
          {
            projectName: 'agent-client-v2',
            logStoreName: mode,
            logGroup: {
              logs: [
                {
                  time: Math.floor(+loggingEvent.startTime / 1000),
                  contents: [
                    {
                      key: 'content',
                      value: layout(loggingEvent, config.timezoneOffset)
                    }
                  ]
                }
              ],
              source: clientId,
              topic
            }
          },
          (err, data) => {
            // console.log('putLogs', err, data);
          }
        );
      };
    }
  }
});

const clsAppender = (topic) => ({
  type: {
    configure: (config, layouts, _findAppender, _levels) => {
      let layout = layouts.basicLayout;
      if (config.layout) {
        layout = layouts.layout(config.layout.type, config.layout);
      }

      return async (loggingEvent) => {
        // console.log('slsAppender', loggingEvent, config, layouts, findAppender, levels);
        const item = new LogItem();
        item.pushBack(new Content('content', layout(loggingEvent, config.timezoneOffset)));
        item.pushBack(new Content('mode', mode));
        item.pushBack(new Content('clientId', clientId));
        item.pushBack(new Content('topic', topic));
        item.setTime(Math.floor(+loggingEvent.startTime / 1000));

        const loggroup = new LogGroup();
        loggroup.addLogs(item);
        const request = new PutLogsRequest(topicID, loggroup);
        await cls.PutLogs(request);
      };
    }
  }
});

/**
 * oss路径 /muyan-browser/logs/2024-11-06/<EMAIL>/browser/13-39-43.log
 *        /muyan/logs/2024-11-07/DE105269/endpoint.dongchacat.cn/慕思旗舰店:森森/1ef90dca-5a5c-60a3-b9f1-7a2673085988_千牛(静默)/00-00-00.log
 *
 */
const ossAppender = (customPath, startTime: dayjs.Dayjs) => {
  let memory;

  return {
    type: {
      configure: (config, layouts, _findAppender, _levels) => {
        if (memory) {
          return memory;
        }
        let layout = layouts.basicLayout;
        if (config.layout) {
          layout = layouts.layout(config.layout.type, config.layout);
        }

        const appendLock = new Lock();
        const date = startTime.format('YYYY-MM-DD');
        let day = startTime;
        let ossPath = join(
          mode,
          date,
          clientId,
          customPath + '_' + day.format('HH-mm-ss') + '.log'
        );
        let result: { nextAppendPosition?: number } = {};

        memory = async (loggingEvent) => {
          try {
            await appendLock.acquire();
            const today = dayjs();
            if (!today.isSame(day, 'day')) {
              // ossPath = getNextDayPath();
              ossPath = join(
                mode,
                today.format('YYYY-MM-DD'),
                clientId,
                customPath + '_' + date + '-continued.log'
              );
              day = today;
              result = {};
            }
            result = await oss.append(
              ossPath,
              Buffer.from(layout(loggingEvent, config.timezoneOffset) + '\n'),
              {
                position: result.nextAppendPosition
              }
            );
          } catch (e: any) {
            if (e.code === 'PositionNotEqualToLength') {
              // 当response error时，请求是成功的，但是没有收到nextAppendPosition，因此会出现这个问题
              // e._result = result;
              // ossPath = getBackoffPath();
              ossPath = join(
                mode,
                dayjs().format('YYYY-MM-DD'),
                clientId,
                customPath + '_' + dayjs().format('HH-mm-ss-[pnetl.log]')
              );
              result = {};
            }
            throw e;
          } finally {
            appendLock.release();
          }
        };
        return memory;
      }
    }
  };
};

const configure = {
  appenders: {
    stdout: stdoutAppender()
  },
  categories: {}
};

/**
 * 每次getLogger会新建一个category(这个是log4js的概念),每个category对应4个appender（stdout、file、oss、sls）
 * integration会存在启停，需要重新设置oss appender的文件时间
 * 但是要避免重置其他已经注册的appender
 */
export function getLogger(topic: string = 'browser'): log4js.Logger {
  const category = topic === 'browser' ? 'default' : topic;
  const filePath = topic === 'browser' ? '.' : 'integrations/' + topic;
  configure.appenders[topic + '-file'] = fileAppender(
    join(app.getPath('logs'), filePath, 'app.log')
  );
  configure.appenders[topic + '-oss'] = ossAppender(topic, dayjs());
  // configure.appenders[topic + '-sls'] = slsAppender(topic);
  configure.appenders[topic + '-cls'] = clsAppender(topic);
  configure.categories[category] = {
    appenders: ['stdout', topic + '-file', topic + '-oss', /*topic + '-sls',*/ topic + '-cls'],
    level: 'ALL'
  };

  log4js.configure(configure);
  return log4js.getLogger(category);
}

export const log = getLogger();

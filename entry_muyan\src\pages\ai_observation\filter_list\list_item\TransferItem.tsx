import React from 'react';
import style from './index.module.css';
// import icon_ewm from "../../../../assets/icons/icon_ewm.svg";
// import { Popover } from "antd";
// import QRCode from "qrcode.react";
import { Tooltip } from 'antd';
import userAvatar from '@/assets/icons/user_avatar.svg';
type Props = {
  active?: boolean;
  data: any;
  url: string;
  name: string;
};
const TransferItem: React.FC<Props> = ({ active, data }) => {
  const flexStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  };
  const stateMap = new Map().set('success', '#44D961').set('failed', '#FF4444');

  const stateTipMap = new Map().set('success', '转接成功').set('failed', '转接失败');
  return (
    <div className={`${style.item} ${active ? style.active : ''}`}>
      <Tooltip
        title={stateTipMap.get(data?.status) ?? data?.status}
        color={stateMap.get(data?.status) ?? '#808080'}
      >
        <div
          className={style.state}
          style={{
            backgroundColor: stateMap.get(data?.status) ?? '#808080'
          }}
        />
      </Tooltip>
      <div style={flexStyle}>
        <div style={flexStyle}>
          <img className={style.icon} src={userAvatar} />
          <div className={style.title}>{data?.mall_name ?? ''}</div>
        </div>
      </div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between'
        }}
      >
        <div className={style.time}>{data?.customer_id}</div>
        {/* <Popover
          content={
            <QRCode
              value={
                url +
                "/#/mobile_operation/?" +
                encodeURIComponent(
                  `threadId=${data.conversation_id}&transfer=${data}&name=${name}&userId=${JSON.parse(localStorage.getItem("user") ?? "{}")?.id ?? ""}&assistantId=${localStorage.getItem("assistantId")}&key=${localStorage.getItem("secret_key")}&org=${localStorage.getItem("organization")}&user=${localStorage.getItem("user")}`,
                )
              }
              style={{
                width: "120px",
                height: "120px",
                marginLeft: "30px",
              }}
            />
          }
          title="二维码"
          trigger="hover"
        >
          <img
            style={{
              width: "16px",
              height: "16px",
              marginTop: "13px",
            }}
            src={icon_ewm}
            alt="icon"
          />
        </Popover> */}
      </div>
    </div>
  );
};

export default TransferItem;

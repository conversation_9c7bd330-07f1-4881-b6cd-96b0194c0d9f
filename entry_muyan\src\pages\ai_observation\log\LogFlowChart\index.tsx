/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Graph, Path, Cell } from '@antv/x6';
import { Selection } from '@antv/x6-plugin-selection';
import insertCss from 'insert-css';
import { register } from '@antv/x6-react-shape';
import { useRef, useEffect } from 'react';
import rag_dag from './data/rag_dag.json';
import rag_node_status from './data/rag_node_status.json';
import inquiry_dag from './data/inquiry_dag.json';
import inquiry_node_status from './data/inquiry_node_status.json';
import aislogo from './imgs/aislogo.png';
import success from './imgs/success.png';
import running from './imgs/running.png';
import failed from './imgs/failed.png';
import not from './imgs/not.png';
import cloneDeep from 'lodash-es/cloneDeep';
import { NodeContent } from './NodeContent';
import { Tooltip } from 'antd';
interface NodeStatus {
  id: string;
  status: 'default' | 'success' | 'failed' | 'running' | 'not';
  label?: string;
  meta_data: any;
  elapsed_time: number;
}

const image = {
  aislogo,
  success,
  failed,
  running,
  not
};

const AlgoNode = (props) => {
  const { node } = props;
  const data = node?.getData() as NodeStatus;
  const { label, status = 'default', meta_data, elapsed_time } = data;
  const startAndEnd = !['开始', '结束'].includes(label ?? '');
  const islogo = !['开始', '结束', '原话回复', '预设消息回答'].includes(label ?? '');
  console.log(['not'].includes(status) || ['开始', '结束'].includes(label ?? ''), label);

  return (
    <NodeContent
      title={label ?? ''}
      disabled={['not'].includes(status) || ['开始', '结束'].includes(label ?? '')}
      jsonData={meta_data ?? {}}
    >
      <div className={`node ${status} cursor-pointer`}>
        {islogo && (
          <img
            src={image.aislogo}
            style={{
              width: '16px',
              height: '16px'
            }}
            alt="logo"
          />
        )}
        <span className={`label ${!islogo ? 'islogo' : ''} ${!startAndEnd ? 'startAndEnd' : ''}`}>
          {label}
        </span>
        {startAndEnd && (
          <span className="status">
            {status === 'success' && (
              <span
                style={{
                  fontSize: '12px',
                  color: '#666666',
                  position: 'relative',
                  top: '-2px'
                }}
              >
                {elapsed_time > 1 ? (
                  `${elapsed_time.toFixed(1)}s`
                ) : (
                  <>
                    <Tooltip placement="top" title={`${(elapsed_time * 1000).toFixed(0)}ms`}>
                      <span>{'< 1s'}</span>
                    </Tooltip>
                  </>
                )}
              </span>
            )}
            {status === 'failed' && (
              <img
                src={image.failed}
                style={{
                  width: '20px',
                  height: '20px'
                }}
                alt="failed"
              />
            )}
            {status === 'running' && (
              <img
                src={image.running}
                style={{
                  width: '20px',
                  height: '20px'
                }}
                alt="running"
              />
            )}
            {status === 'not' && (
              <img
                src={image.not}
                style={{
                  width: '11px',
                  height: '11px',
                  marginLeft: '9px !important'
                }}
                alt="not"
              />
            )}
          </span>
        )}
      </div>
    </NodeContent>
  );
};

const zhMap = new Map()
  .set('用户消息分析', 'init')
  .set('基于规则的消息生成', 'rule_based_gen')
  .set('判断能否解答', 'kedang_transfer')
  .set('用户消息改写', 'iur')
  .set('知识检索', 'coarse_recall')
  .set('检索结果合并', 'recall_merge')
  .set('检索结果排序', 'rank')
  .set('预置话术规则', 'preset')
  .set('生成回答', 'answer');

export const AIModelDAG = ({ metadata }: { metadata: any }) => {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const isInquiry = localStorage.getItem('recovery_mode') === 'inquiry';
  const data = isInquiry ? inquiry_dag : rag_dag;
  const nodeStatus = isInquiry ? inquiry_node_status : rag_node_status;
  const getWindowHeight = () => {
    return (
      (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) -
      130
    );
  };
  let timer;
  // 显示节点状态
  const showNodeStatus = async (statusList: NodeStatus[][], graph) => {
    const status = statusList.shift();
    status?.forEach((item) => {
      const { id, status } = item;
      const node = graph.getCellById(id);
      const data = node.getData() as NodeStatus;
      node.setData({
        ...data,
        status
      });
    });

    timer = setTimeout(() => {
      if (timer) clearTimeout(timer);
      if (status && status?.length) showNodeStatus(statusList, graph);
    }, 1000);
  };
  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.innerHTML = '';
      register({
        shape: 'dag-node',
        width: 140,
        height: 36,
        component: AlgoNode,
        ports: {
          groups: {
            top: {
              position: 'top',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#C2C8D5',
                  strokeWidth: 1,
                  fill: '#fff'
                }
              }
            },
            bottom: {
              position: 'bottom',
              attrs: {
                circle: {
                  r: 4,
                  magnet: true,
                  stroke: '#C2C8D5',
                  strokeWidth: 1,
                  fill: '#fff'
                }
              }
            }
          }
        }
      });

      Graph.registerEdge(
        'dag-edge',
        {
          inherit: 'edge',
          attrs: {
            line: {
              stroke: '#C2C8D5',
              strokeWidth: 1,
              targetMarker: null
            }
          }
        },
        true
      );

      Graph.registerConnector(
        'algo-connector',
        (s, e) => {
          const offset = 4;
          const deltaY = Math.abs(e.y - s.y);
          const control = Math.floor((deltaY / 3) * 2);

          const v1 = { x: s.x, y: s.y + offset + control };
          const v2 = { x: e.x, y: e.y - offset - control };

          return Path.normalize(
            `M ${s.x} ${s.y}
             L ${s.x} ${s.y + offset}
             C ${v1.x} ${v1.y} ${v2.x} ${v2.y} ${e.x} ${e.y - offset}
             L ${e.x} ${e.y}
            `
          );
        },
        true
      );

      const graph: Graph = new Graph({
        container: containerRef.current,
        width: 340,
        height: getWindowHeight(),
        panning: {
          enabled: true,
          eventTypes: ['leftMouseDown', 'mouseWheel']
        },
        mousewheel: {
          enabled: true,
          modifiers: 'ctrl',
          factor: 1.1,
          maxScale: 1.5,
          minScale: 0.5
        },
        highlighting: {
          magnetAdsorbed: {
            name: 'stroke',
            args: {
              attrs: {
                fill: '#fff',
                stroke: '#31d0c6',
                strokeWidth: 4
              }
            }
          }
        },
        connecting: {
          snap: true,
          allowBlank: false,
          allowLoop: false,
          highlight: true,
          connector: 'algo-connector',
          connectionPoint: 'anchor',
          anchor: 'center',
          validateMagnet({ magnet }) {
            return magnet.getAttribute('port-group') !== 'top';
          },
          createEdge() {
            return graph.createEdge({
              shape: 'dag-edge',
              attrs: {
                line: {
                  strokeDasharray: '5 5'
                }
              },
              zIndex: -1
            });
          }
        },
        interacting: {
          nodeMovable: false
        }
      });
      graph.use(
        new Selection({
          multiple: true,
          rubberEdge: true,
          rubberNode: true,
          modifiers: 'shift',
          rubberband: true
        })
      );

      graph.on('edge:connected', ({ edge }) => {
        edge.attr({
          line: {
            strokeDasharray: ''
          }
        });
      });

      graph.on('node:change:data', ({ node }) => {
        const edges = graph.getIncomingEdges(node);
        const { status } = node.getData() as NodeStatus;
        edges?.forEach((edge) => {
          if (status === 'running') {
            edge.attr('line/strokeDasharray', 5);
            edge.attr('line/style/animation', 'running-line 30s infinite linear');
          } else {
            edge.attr('line/strokeDasharray', '');
            edge.attr('line/style/animation', '');
          }
        });
      });

      // 初始化节点/边
      const init = (data: Cell.Metadata[]) => {
        const cells: Cell[] = [];
        data.forEach((item) => {
          if (item.shape === 'dag-node') {
            cells.push(graph.createNode(item));
          } else {
            cells.push(graph.createEdge(item));
          }
        });
        graph.resetCells(cells);
      };
      const newData = cloneDeep(data);
      newData.forEach((item) => {
        if (item?.data) {
          const meta_data = metadata?.[zhMap.get(item.data.label)] ?? {};
          item.data.meta_data = meta_data;
          item.data.elapsed_time = meta_data?.elapse ?? '';
          if (item.data.label === '推理分析') {
            item.data.meta_data = {
              candidates: metadata?.answer_meta?.candidates,
              selected: metadata?.answer_meta?.selected,
              reason: metadata?.answer?.reason
            };
          }
          if (item.data.label === '预设消息回答') {
            item.data.meta_data = {
              cls_label: metadata?.answer_meta?.cls_label
            };
          }
          if (item.data.label === '原话回复') {
            item.data.meta_data = {
              candidates: metadata?.answer_meta?.candidates,
              selected: metadata?.answer_meta?.selected
            };
          }
        }
      });

      init(newData);
      if (metadata?.answer_meta?.candidates) {
        if (metadata?.answer_meta?.candidates?.length === 0) {
          showNodeStatus(retrievalFailure(), graph);
        } else {
          if (metadata?.answer_meta?.selected?.length === 0) {
            showNodeStatus(reasoningfailure(), graph);
          } else {
            showNodeStatus(cloneDeep(nodeStatus[metadata?.answer_meta?.answer_type] ?? []), graph);
          }
        }
      } else {
        showNodeStatus(retrievalFailure(), graph);
      }
      graph.centerContent();
      insertCss(`
      .node {
        display: flex;
        align-items: center;
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        border: 1px solid #c2c8d5;
        border-left: 4px solid #5F95FF;
        border-radius: 4px;
        box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);
      }
      .node img {
        flex-shrink: 0;
        margin-left: 4px;
      }
      .node .label {
        display: inline-block;
        flex-shrink: 0;
        width: 80px;
        margin-left: 4px;
        color: #666;
        font-size: 12px;
      }
      .node .status {
        flex-shrink: 0;
      }
      .node.success {
        border-left: 4px solid #52c41a;
      }
      .node.failed {
        border-left: 4px solid #ff4d4f;
      }
      .node.not {
        border-left: 4px solid #666666;
      }
      .node.running .status img {
        animation: spin 1s linear infinite;
      }
      .node .islogo {
        padding-left: 10px;
        width: 98px !important;
      }
      .node .startAndEnd {
        text-align: center !important;
        width: 110px !important;
      }
      .x6-node-selected .node {
        border-color: #1890ff;
        border-radius: 2px;
        box-shadow: 0 0 0 4px #d4e8fe;
      }
      .x6-node-selected .node.success {
        border-color: #52c41a;
        border-radius: 2px;
        box-shadow: 0 0 0 4px #ccecc0;
      }
      .x6-node-selected .node.failed {
        border-color: #ff4d4f;
        border-radius: 2px;
        box-shadow: 0 0 0 4px #fedcdc;
      }
      .x6-edge:hover path:nth-child(2){
        stroke: #1890ff;
        stroke-width: 1px;
      }

      .x6-edge-selected path:nth-child(2){
        stroke: #1890ff;
        stroke-width: 1.5px !important;
      }

      @keyframes running-line {
        to {
          stroke-dashoffset: -1000;
        }
      }
      @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
      }
      `);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, []);

  const updataStatus = (arr: any[], ids: string[], status: string) => {
    arr.forEach((element) => {
      if (ids.includes(element.id)) element.status = status;
    });
  };

  const retrievalFailure = () => {
    const status = cloneDeep(nodeStatus[metadata?.answer_meta?.answer_type] ?? []);
    status.forEach((item, index) => {
      if (index > 3) {
        updataStatus(item, ['5', '6', '7', '17', '16'], 'not');
        updataStatus(item, ['4'], 'failed');
      }
    });
    return status;
  };

  const reasoningfailure = () => {
    const status = cloneDeep(nodeStatus[metadata?.answer_meta?.answer_type] ?? []);
    status.forEach((item, index) => {
      if (index > 6) {
        updataStatus(item, ['7', '16'], 'not');
        updataStatus(item, ['17'], 'failed');
      }
    });
    return status;
  };
  return <div ref={containerRef} />;
};

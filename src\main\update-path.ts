import { app } from 'electron';
import path from 'node:path';

const mode = import.meta.env.MODE;

if (mode !== 'development' && mode !== 'production') {
  app.setPath('userData', path.join(app.getPath('appData'), mode));
  app.setPath('logs', path.join(app.getPath('logs'), '..', mode));
  app.setPath('temp', path.join(app.getPath('temp'), mode));
} else {
  app.setPath('temp', path.join(app.getPath('temp'), app.name));
}

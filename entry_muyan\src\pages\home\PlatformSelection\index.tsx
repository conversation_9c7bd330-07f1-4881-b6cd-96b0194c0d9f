/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import style from './index.module.css';
// import Button from "../../../components/Button";
import { Modal, Button } from 'antd';
import React from 'react';
import { message } from 'antd';
import { useConfigStates } from '../utils/configItemStates';
import { addConfig } from '@/api/myqaApi';
import { cardData /*openCardPage*/ } from '../utils/cardData';

const Card = ({
  isOpen,
  onClick,
  src,
  title
}: {
  isOpen: boolean;
  onClick: () => void;
  src: string;
  title: string;
}): React.ReactElement => {
  return (
    <div className={style.card}>
      <img className={style.modal_img_icon} src={src} alt="icon" />
      <div className={style.modal_title}> {title}</div>
      <Button
        className={style.modal_button}
        onClick={() => {
          if (isOpen) onClick();
          else message.success('申请已提交');
        }}
        size="small"
        type={isOpen ? 'primary' : 'default'}
      >
        {isOpen ? '去授权' : '去开通'}
      </Button>
    </div>
  );
};

type Props = {
  open: boolean;
  data: any[];
  getData: (val?: string) => void;
  setOpen: (val: boolean) => void;
};
function escapeRegex(string) {
  return string.replace(/[/\-\\^$*+?.()|[\]{}]/g, '\\$&');
}
const PlatformSelection: React.FC<Props> = ({ open, setOpen, getData, data }) => {
  const { updateListState: setStateList } = useConfigStates();
  const onCreate = (type: string, name: string): void => {
    // if (['qianniu', 'qianniu-silence'].includes(type)) {
    //   const qianniu = data.find((item) => ['qianniu'].includes(item.type));
    //   if (qianniu && type === 'qianniu') {
    //     message.warning('千牛账户已存在');
    //     return;
    //   }
    //   const qianniuSilence = data.find((item) => ['qianniu-silence'].includes(item.type));
    //   console.log(qianniu, qianniuSilence);
    //   if (qianniuSilence && type === 'qianniu-silence') {
    //     message.warning('千牛静默账户已存在');
    //     return;
    //   }
    // }
    let max = -1;
    let newName = name;
    const regex = new RegExp(`^${escapeRegex(name)}(\\d+)?$`);
    data.forEach((item) => {
      if (item.type === type) {
        const match = item.name.match(regex);
        if (match) {
          if (!match[1]) {
            max = Math.max(max, 1);
          } else {
            max = Math.max(max, +match[1]);
          }
        }
      }
    });
    if (max >= 0) {
      newName = `${name}${max + 1}`;
    }

    addConfig({
      assistant_id: localStorage.getItem('assistantId'),
      mall_name: newName,
      platform: type,
      meta_data: {
        // isAccredit: !['qianniu', 'qianniu-silence'].includes(type)
        isAccredit: false
      }
    }).then((res: any) => {
      setOpen(false);
      setStateList('add', { id: res.data.id, state: 'ai' });
      getData(res.data.id);
      // try {
      //   openCardPage(res.data.platform, res.data.id);
      // } catch (error) {
      //   console.log('handleArgenting', error);
      // }
    });
  };

  return (
    <Modal
      title="平台选择"
      footer={false}
      open={open}
      centered
      onCancel={() => setOpen(false)}
      width={800}
    >
      <div className={style.modal_content}>
        <div className={style.modal_scroll}>
          {cardData.map((item, index) => (
            <div key={index}>
              <Card
                title={item.title}
                isOpen={item.isOpen}
                src={item.src}
                onClick={() => {
                  onCreate(item.type, item.name);
                }}
              />
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
};

export default PlatformSelection;

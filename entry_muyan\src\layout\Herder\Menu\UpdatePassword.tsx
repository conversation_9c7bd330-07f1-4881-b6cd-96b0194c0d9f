import { Input, Button, Form, Modal, message } from 'antd';
import { editPassword } from '@/api/myqaApi';
type Props = {
  isModalOpen: boolean;
  setIsModalOpen: (val: boolean) => void;
  loginOut: () => void;
};
export const UpdatePassword = ({ isModalOpen, setIsModalOpen, loginOut }: Props) => {
  const [form] = Form.useForm();
  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  const onFinish = async (value) => {
    await editPassword(value).then(() => {
      message.success('密码修改成功，请重新登录');
      handleCancel();
      loginOut();
    });
  };

  return (
    <Modal
      title="修改密码"
      open={isModalOpen}
      onOk={handleOk}
      footer={null}
      width={400}
      onCancel={handleCancel}
      destroyOnClose={true}
    >
      <div style={{ padding: '24px' }}>
        <Form name="normal_login" form={form} className="login-form" onFinish={onFinish}>
          <Form.Item
            label="请输入新密码"
            name="password"
            rules={[{ required: true, message: '请输入新密码!' }]}
          >
            <Input
              placeholder="请输入新密码"
              type="password"
              onPressEnter={() => form.submit()}
            ></Input>
          </Form.Item>
          <Form.Item>
            <Button
              style={{ width: '100%', marginTop: '10px' }}
              onClick={() => form.submit()}
              type="primary"
            >
              确定修改
            </Button>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

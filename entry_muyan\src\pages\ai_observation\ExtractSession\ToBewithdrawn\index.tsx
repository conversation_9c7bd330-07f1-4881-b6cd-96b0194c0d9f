import { observer } from 'mobx-react-lite';
import { Tabs, TabsProps } from 'antd';
import { BewithdrawnQuery } from './ToBewithdrawnQuery';
import { BewithdrawnResponse } from './ToBewithdrawnResponse';
import { ToBewithdrawnFilter } from './ToBewithdrawnFilter';
import { useState } from 'react';
import cloneDeep from 'lodash-es/cloneDeep';
type Props = {
  kb_name: string;
  messageData: any[];
};
export const ToBewithdrawn = observer(({ kb_name, messageData }: Props) => {
  const [filterData, setFilterData] = useState({
    not: true,
    has: true
  });

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '问法',
      children: (
        <BewithdrawnQuery kb_name={kb_name} filterData={filterData} messageData={messageData} />
      )
    },
    {
      key: '2',
      label: '回复',
      children: <BewithdrawnResponse kb_name={kb_name} messageData={messageData} />
    }
  ];

  return (
    <div
      style={{
        width: '300px',
        position: 'relative'
      }}
    >
      <Tabs items={items} />
      <ToBewithdrawnFilter
        style={{
          position: 'absolute',
          right: '-4px',
          top: '16px',
          cursor: 'pointer'
        }}
        onChange={(value, key) => {
          const data = cloneDeep(filterData);
          data[key] = value;
          setFilterData(data);
        }}
      />
    </div>
  );
});

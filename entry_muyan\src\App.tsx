import Layout from './layout';
import './style/app.css';
import './style/antd.css';
import { ConfigProvider, Empty } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import default_content from './assets/imgs/default_content.png';
import { ConfirmationDialogProvider } from './components/ConfirmationDialog';
import { PortProvider } from '@/contexts/PortContext';
import { IsDevProvider } from '@/contexts/IsDevContext';
function App() {
  return (
    <div className="app">
      <ConfigProvider
        locale={zhCN}
        renderEmpty={() => <Empty image={default_content} />}
        theme={{
          cssVar: true,
          token: {
            colorPrimary: '#2e74ff',
            colorInfo: '#2e74ff',
            colorSuccess: '#0ac448',
            colorWarning: '#ffbf00',
            colorError: '#f23c3c',
            colorPrimaryHover: '#4794ff',
            colorPrimaryTextHover: '#4794ff',
            colorPrimaryTextActive: '#2958e5',
            colorPrimaryActive: '#2958e5',
            colorPrimaryBorder: '#94b8ff',
            colorPrimaryBg: '#e5f0ff',
            colorPrimaryBgHover: '#e5f0ff',
            colorPrimaryBorderHover: '#4794ff',
            colorSuccessBorder: '#82e0a2',
            colorSuccessBg: '#e0ffeb',
            colorSuccessBgHover: '#e0ffeb',
            colorWarningBorder: '#ffdd99',
            colorWarningBg: '#fff5e0',
            colorWarningBgHover: '#fff5e0',
            colorWarningBorderHover: '#ffdd99',
            colorSuccessBorderHover: '#82e0a2',
            colorErrorBorderHover: '#ff9999',
            colorErrorBorder: '#ff9999',
            colorErrorBgHover: '#ffe5e5',
            colorErrorBg: '#ffe5e5',
            colorTextBase: '#121212',
            colorTextSecondary: '#595b60',
            colorTextTertiary: '#7c7c7c',
            colorTextQuaternary: '#ababab',
            colorBorder: '#dadada',
            colorBorderSecondary: '#dadada',
            borderRadius: 8
          },
          components: {
            Button: {
              controlHeightSM: 28
            },
            Form: {
              labelColor: 'rgb(18,18,18)'
            },
            Tabs: {
              inkBarColor: 'rgb(46,116,255)',
              itemColor: 'rgb(89,91,96)',
              itemActiveColor: 'rgb(18,18,18)',
              itemHoverColor: 'rgb(18,18,18)',
              itemSelectedColor: 'rgb(18,18,18)'
            },
            Upload: {
              marginXS: 12
            },
            Input: {
              borderRadius: 8
            },
            Select: {
              borderRadius: 8
            }
          }
        }}
      >
        <PortProvider>
          <IsDevProvider>
            <Layout />
          </IsDevProvider>
        </PortProvider>
      </ConfigProvider>
      <ConfirmationDialogProvider />
    </div>
  );
}

export default App;

/* eslint-disable react/prop-types */
import style from './index.module.css';
import { Input, DatePicker } from 'antd';
import icon_interface_search from '@/assets/icons/icon_interface_search.svg';
import { useEffect, useState, useMemo } from 'react';
import dayjs from 'dayjs';
import debounce from 'lodash-es/debounce';
import { threadSearch, searchAll, threadBesilentSearch } from '@/api/myqaApi';
const { RangePicker } = DatePicker;
type Props = {
  value: string;
  serValue: (val) => void;
  setData: (val, list?, search?) => void;
  setLoading: (boolean) => void;
  setFileList: (val) => void;
  onChange: (val) => void;
  sub: number;
  viewKey: string;
  setStartTime: (val: any) => void;
};
const SearchThread: React.FC<Props> = ({
  value,
  serValue,
  setData,
  setLoading,
  setFileList,
  onChange,
  sub,
  viewKey,
  setStartTime
}) => {
  const [time, setTime] = useState<any>([
    dayjs(dayjs(new Date()).format('YYYY/MM/DD 00:00:00')),
    dayjs(dayjs(new Date()).format('YYYY/MM/DD 23:59:59'))
  ]);

  const onOk = (val: any): void => {
    let range = val as any;
    if (val && dayjs(val[0]) > dayjs(val[1])) {
      range = [val[1], val[0]];
    } else {
      range = val;
    }
    setStartTime(dayjs(range[0]).valueOf());
    setTime(range);
    getData(range).then(() => {
      if (value) {
        domainSearch(value);
      }
    });
  };
  useEffect(() => {
    getData(time, true);
  }, [sub, viewKey]);
  const getData = (
    val: any[],
    isChange?: boolean,
    fileList?: any[],
    search?: any
  ): Promise<void> => {
    setLoading(true);
    const argument = {
      start_time: dayjs(val[0]).valueOf(),
      end_time: dayjs(val[1]).valueOf()
      // source: viewKey === "1" ? ["manual"] : ["agent"],
    };

    const request = viewKey === '3' ? threadSearch(argument) : threadBesilentSearch(argument);
    return request
      .then((res) => {
        setData(res.data, fileList, search);
        console.log(res.data, fileList, search);
        if (isChange && fileList !== undefined) onChange(res.data[0].id);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const domainSearch = useMemo(
    () =>
      debounce((value: string) => {
        setFileList([]);
        if (value) {
          const data = getSearchAllData(value, 'all');

          setLoading(true);
          searchAll({ ...data })
            .then((res) => {
              setFileList(res.data);
              getData(time, false, res.data, value);
            })
            .finally(() => {
              setLoading(false);
            });
        } else {
          getData(time, false, [], '');
        }
      }, 800),
    [time, viewKey]
  );
  const getSearchAllData = (value: string, type?: string): any => {
    const data = {
      search: '',
      assistant_id: localStorage.getItem('assistantId'),
      communication_id: '',
      conversation_id: '',
      run_id: '',
      thread_id: '',
      message_id: '',

      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    };
    // 根据不同类型的搜索设置对应的数据项
    switch (type) {
      case 'all':
        return getSearchAllData(value, value?.split('_')?.[0] ?? '');
      // case "asst":
      //   data.assistant_id = value;
      //   break;
      case 'run':
        data.run_id = value;
        break;
      case 'thread':
        data.thread_id = value;
        break;
      case 'msg':
        data.message_id = value;
        break;
      default:
        data.search = value;
        break;
    }
    return data;
  };
  return (
    <div className="filter" style={{ marginBottom: '16px' }}>
      <Input
        placeholder="搜索用户消息"
        className={style.input}
        prefix={<img src={icon_interface_search} alt="icon" className={style.icon} />}
        value={value}
        onChange={(e: any) => {
          serValue(e.target.value);
          domainSearch(e.target.value);
        }}
      />
      {/* <Select
        className={style.select}
        defaultValue="pdd"
        options={[{ value: "pdd", label: "平台（拼多多）" }]}
      /> */}
      <RangePicker
        style={{
          marginTop: '12px'
        }}
        showTime={{
          format: 'HH:mm:ss',
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')]
        }}
        value={time}
        format="YYYY/MM/DD HH:mm:ss"
        onOk={onOk}
      />
    </div>
  );
};

export default SearchThread;

import {
  BaseWindow,
  ipcMain,
  WebContentsView,
  MessageChannelMain,
  WebContents,
  powerSaveBlocker,
  dialog,
  app,
  shell,
  Menu,
  globalShortcut,
  BrowserWindow
} from 'electron';
import { store } from './store';
import { join } from 'node:path';
import { is } from '@electron-toolkit/utils';
import { log } from './log';
import urlRegexSafe from 'url-regex-safe';
import Tab, { TabOptions } from './tab';
import _ from 'lodash';
import menuTemplate from './menu';
import TokenizeFeatures from 'tokenize-features';
import { createPromiseCapability } from './utils/promise';
import { randomUUID } from 'node:crypto';

export type Homepage = {
  url: string;
  options: Pick<TabOptions, 'integration' | 'advances'>;
};

export type WindowType = 'normal' | 'popup' | 'frameless';

export type WindowConstructorOptions = {
  features?: string;
  openTab?: {
    url: string;
    options?: Partial<TabOptions>;
  };
};

export default class Window {
  static #windowMap = new Map<BaseWindow, Window>();
  static isAppQuiting = false;

  id: string;

  #windowWidth: number;
  #windowHeight: number;
  #toolbarHeight: number;
  #addressBarHeight: number = 32;

  #window: BaseWindow;
  #windowType: WindowType;
  #toolbar: WebContentsView;
  #toolbarInitialized: ReturnType<typeof createPromiseCapability>;
  #hideToolbar: boolean;
  #hideAddressBar: boolean;
  #hideFavicon: boolean;

  #homepage: Homepage | undefined;
  #features: TokenizeFeatures | undefined;

  #isClosed: boolean = false;

  #tabs: Tab[] = [];
  #activeTab: Tab;

  #powerSaveBlocker: {
    type: 'system' | 'display' | 'unset';
    id?: number;
  } = {
    type: 'unset'
  };

  static {
    this.setMenu();
  }

  constructor(options?: WindowConstructorOptions) {
    if (options?.features) {
      this.#features = new TokenizeFeatures(options?.features);
    }
    this.id = randomUUID();
    this.setHomepage();
    this.initWindow();
    this.initWindowEvent();
    this.initToolbar();
    this.newTab(options?.openTab?.url, options?.openTab?.options);
  }

  static get size() {
    return this.#windowMap.size;
  }

  static setMenu() {
    const template = menuTemplate(undefined, [
      {
        menuIndex: process.platform === 'darwin' ? 3 : 2,
        index: 2,
        deleteCount: 1,
        menu: {
          label: 'Toggle Developer Tools',
          accelerator: (function () {
            if (process.platform === 'darwin') return 'Alt+Command+I';
            else return 'Ctrl+Shift+I';
          })(),
          click: (item, focusedWindow) => {
            if (focusedWindow) this.#windowMap.get(focusedWindow)?.toggleDevTools();
          }
        }
      }
    ]);

    Menu.setApplicationMenu(Menu.buildFromTemplate(template));
  }

  static closeAll() {
    this.isAppQuiting = true;
    this.#windowMap.forEach((_, baseWindow) => {
      baseWindow.close();
    });
  }

  get activeTab(): Tab {
    return this.#activeTab;
  }

  setHomepage() {
    const homepage = store.get('homepage');

    if (typeof homepage === 'string') {
      this.#homepage = {
        url: homepage,
        options: {}
      };
    } else if (typeof homepage === 'object') {
      this.#homepage = {
        url: homepage.url,
        options: homepage.options
      };
    }
  }

  setWindowType() {
    this.#windowType = this.#features?.isSet('frameless')
      ? 'frameless'
      : this.#features?.isSet('popup')
        ? 'popup'
        : 'normal';
    this.#hideToolbar = this.#windowType !== 'normal';
  }

  setWindowSize() {
    this.#windowWidth = this.#features?.get('width')
      ? parseInt(this.#features?.get('width'))
      : store.get('window.width');
    this.#windowHeight = this.#features?.get('height')
      ? parseInt(this.#features?.get('height'))
      : store.get('window.width');
  }

  initWindow() {
    this.setWindowType();
    this.setWindowSize();
    this.#window = new BaseWindow({
      title: store.get('window.title'),
      width: this.#windowWidth,
      height: this.#windowHeight,
      autoHideMenuBar: true,
      ...(this.#windowType === 'popup' ? {} : {}),
      ...(this.#windowType === 'frameless'
        ? {
            frame: false,
            transparent: this.#features?.isSet('transparent'),
            skipTaskbar: true
          }
        : {}),
      ...(this.#features?.get('left') !== undefined && this.#features?.get('top') !== undefined
        ? {
            x: parseInt(this.#features?.get('left')),
            y: parseInt(this.#features?.get('top'))
          }
        : {}),
      ...(this.#features?.get('resizable') !== undefined
        ? {
            resizable: this.#features?.isSet('resizable')
          }
        : {})
    });
    Window.#windowMap.set(this.#window, this);
  }

  initWindowEvent() {
    this.#window.on('resize', (...args) => {
      log.debug('window', this.id, 'resize', ...args);
      this.onWindowResize();
    });
    this.#window.on('resized', (...args) => {
      log.debug('window', this.id, 'resized', ...args);
      // onResize(window, toolbar, mainView);
    });
    this.#window.on('maximize', (...args) => {
      log.debug('window', this.id, 'maximize', ...args);
      // onResize(window, toolbar, mainView);
    });
    this.#window.on('close', (e) => {
      log.debug('window', this.id, 'window close');
      this.close(e);
      // onResize(window, toolbar, mainView);
    });
    this.#window.on('closed', (...args) => {
      log.debug('window', this.id, 'window closed', ...args);
      this.closed();
    });
    this.#window.on('focus', (...args) => {
      log.debug('window', this.id, 'window focus', ...args);
      this.registerShortcut();
    });
    this.#window.on('blur', (...args) => {
      log.debug('window', this.id, 'window blur', ...args);
      this.unregisterShortcut();
    });
  }

  initToolbar() {
    this.#toolbarInitialized = createPromiseCapability();
    this.#toolbar = new WebContentsView({
      webPreferences: {
        preload: join(__dirname, '../preload/toolbar.js')
      }
    });
    // HMR for renderer base on electron-vite cli.
    // Load the remote URL for development or the local html file for production.
    if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
      this.#toolbar.webContents.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/toolbar/');
    } else {
      this.#toolbar.webContents.loadFile(join(__dirname, '../renderer/toolbar/index.html'));
    }

    this.#window.contentView.addChildView(this.#toolbar);
    this.initToolbarIpc();

    this.setHideAddressBar(store.get('window.hideAddressBar') ?? false);
    this.setHideFavicon(store.get('window.hideFavicon') ?? false);
  }

  initToolbarIpc() {
    this.#toolbar.webContents.on('did-finish-load', (...args) => {
      log.debug('window', this.id, 'toolbar did-finish-load', ...args);
      this.#toolbarInitialized.resolve();
    });

    this.#toolbar.webContents.ipc.on('change-url', (e, url) => {
      let _url = url;
      if (!urlRegexSafe({ exact: true }).test(_url)) {
        _url = 'https://www.google.com/search?q=' + _url;
      }

      if (!/^https?:\/\//.test(_url)) {
        _url = 'http://' + _url;
      }

      log.debug('window', this.id, 'change-url', _url);
      this.#activeTab.loadURL(_url);
    });

    this.#toolbar.webContents.ipc.on('go-back', () => {
      log.debug('window', this.id, 'go-back');
      this.#activeTab.goBack();
    });
    this.#toolbar.webContents.ipc.on('go-forward', () => {
      log.debug('window', this.id, 'go-forward');
      this.#activeTab.goForward();
    });
    this.#toolbar.webContents.ipc.on('reload', () => {
      log.debug('window', this.id, 'reload');
      this.#activeTab.reload();
    });

    this.#toolbar.webContents.ipc.on('change-tab', (e, data) => {
      log.debug('window', this.id, 'change-tab', data);
      const { action, payload } = data;

      switch (action) {
        case 'new':
          this.newTab();
          break;
        case 'active': {
          const { id } = payload;
          for (const tab of this.#tabs) {
            if (tab.id === id) {
              this.setActiveTab(tab);
              break;
            }
          }
          break;
        }
        case 'close': {
          const { id } = payload;
          for (let i = 0; i < this.#tabs.length; i++) {
            if (this.#tabs[i].id === id) {
              this.#tabs[i].close();
              break;
            }
          }
          break;
        }
      }
    });
  }

  safelySendToToolbar(channel, ...args) {
    this.#toolbarInitialized.promise.then(() => {
      this.#toolbar.webContents.send(channel, ...args);
    });
  }

  shouldAcceptIpcEvent(event) {
    return this.#window.contentView.children.some(
      (view) => (view as WebContentsView).webContents === event.sender
    );
  }

  newTab(url = '', options = {}) {
    const defaultOptions: TabOptions = {
      bounds: {
        x: 0,
        y: this.#toolbarHeight,
        width: this.#window.getContentBounds().width,
        height: this.#window.getContentBounds().height - this.#toolbarHeight
      },
      callbacks: {
        addChildView: this.addChildView.bind(this),
        updateTab: this.updateTab.bind(this),
        newTab: this.newTab.bind(this),
        tabDestroyed: this.tabDestroyed.bind(this),
        beforeUnload: this.beforeUnload.bind(this)
      },
      currentWindow: this
    };
    const _options = _.merge(defaultOptions, options);
    let _url = url;

    if (!_url) {
      if (this.#homepage) {
        _url = this.#homepage.url;
        Object.assign(_options, this.#homepage.options);
      } else {
        if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
          _url = process.env['ELECTRON_RENDERER_URL'] + '/default-view/';
          // _url = 'http://localhost:8172/';
        } else {
          _url = join(__dirname, '../renderer/default-view/index.html');
        }
      }
    }

    const tab = new Tab(_url, _options);
    this.#tabs.push(tab);
    this.safelySendToToolbar('change-tab', {
      action: 'new',
      payload: {
        id: tab.id,
        url: tab.url,
        title: tab.title,
        isLoading: tab.isLoading,
        closeable: tab.closeable
      }
    });
    this.setActiveTab(tab);
    this.callNewTabHooks();
    return tab;
  }

  // TODO Deprecated
  establishChannel(opener: WebContents, newTab: WebContents) {
    const { port1, port2 } = new MessageChannelMain();

    port2.postMessage({ test: 21 });
    opener.postMessage('port', null, [port1]);
    newTab.postMessage('port', null, [port2]);
    port2.postMessage({ test: 23 });
  }

  toggleDevTools() {
    log.debug('window', this.id, 'toggleDevTools');
    if (is.dev) {
      this.#toolbar.webContents.openDevTools({ mode: 'detach' });
    }
    this.#activeTab.toggleDevTools();
  }

  addChildView(view) {
    this.#window.contentView.addChildView(view);
  }

  removeChildView(view) {
    this.#window.contentView.removeChildView(view);
  }

  updateTab(id: number) {
    for (const tab of this.#tabs) {
      if (tab.id === id) {
        this.safelySendToToolbar('change-tab', {
          action: 'update',
          payload: {
            id: tab.id,
            url: tab.url,
            title: tab.title,
            canGoBack: tab.navigationHistory[0],
            canGoForward: tab.navigationHistory[1],
            faviconUrl: tab.faviconUrl,
            isLoading: tab.isLoading
          }
        });
        break;
      }
    }
  }

  beforeUnload(tab: Tab, event) {
    if (tab !== this.#activeTab) {
      this.setActiveTab(tab);
    }
    const choice = dialog.showMessageBoxSync(this.#window as unknown as BrowserWindow, {
      type: 'question',
      buttons: store.get('window.onbeforeunload.buttons'),
      title: store.get('window.onbeforeunload.title'),
      message: store.get('window.onbeforeunload.message'),
      defaultId: 0,
      cancelId: 1
    });
    const leave = choice === 0;
    if (leave) {
      event.preventDefault();
    }
  }

  tabDestroyed(id: number, view: WebContentsView) {
    if (this.#isClosed) {
      return;
    }

    this.safelySendToToolbar('change-tab', {
      action: 'delete',
      payload: {
        id
      }
    });
    this.removeChildView(view);

    const index = this.#tabs.findIndex((tab) => tab.id === id);
    this.#tabs.splice(index, 1);

    if (!this.#tabs.length) {
      this.#window.close();
      // this.newTab()
    } else {
      let nextActiveTabId = index;

      if (nextActiveTabId >= this.#tabs.length) {
        nextActiveTabId = this.#tabs.length - 1;
      }

      this.setActiveTab(this.#tabs[nextActiveTabId]);
      this.callCloseTabHooks();
    }
  }

  callNewTabHooks() {
    this.#window.setMinimizable(this.checkMinimizable());
    this.checkBlockPowerSave();
  }

  callCloseTabHooks() {
    this.#window.setMinimizable(this.checkMinimizable());
    this.checkBlockPowerSave();
  }

  checkMinimizable() {
    return this.#tabs.every((tab) => tab.minimizable);
  }

  checkBlockPowerSave() {
    let system = false;
    let display = false;

    for (const tab of this.#tabs) {
      if (tab.blockPowerSave === 'system') {
        system = true;
      }
      if (tab.blockPowerSave === 'display') {
        display = true;
        break;
      }
    }

    if (this.#powerSaveBlocker.type === 'unset') {
      if (display) {
        this.#powerSaveBlocker.type = 'display';
        this.#powerSaveBlocker.id = powerSaveBlocker.start('prevent-display-sleep');
      } else if (system) {
        this.#powerSaveBlocker.type = 'system';
        this.#powerSaveBlocker.id = powerSaveBlocker.start('prevent-app-suspension');
      }
    } else if (this.#powerSaveBlocker.type === 'system') {
      if (display) {
        powerSaveBlocker.stop(this.#powerSaveBlocker.id!);
        this.#powerSaveBlocker.type = 'display';
        this.#powerSaveBlocker.id = powerSaveBlocker.start('prevent-display-sleep');
      } else if (!system) {
        this.#powerSaveBlocker.type = 'unset';
        powerSaveBlocker.stop(this.#powerSaveBlocker.id!);
      }
    } else if (this.#powerSaveBlocker.type === 'display') {
      if (!display) {
        this.#powerSaveBlocker.type = 'unset';
        powerSaveBlocker.stop(this.#powerSaveBlocker.id!);

        if (system) {
          this.#powerSaveBlocker.type = 'system';
          this.#powerSaveBlocker.id = powerSaveBlocker.start('prevent-app-suspension');
        }
      }
    }
    log.debug('window', this.id, 'powerSaveBlocker status', this.#powerSaveBlocker);
  }

  setTabsBounds() {
    const giveMeSomeAir = this.#tabs.some((tab) => tab.giveMeSomeAir);
    if (giveMeSomeAir) {
      this.onWindowResize();
    }
  }

  setHideAddressBar(hide: boolean) {
    this.#hideAddressBar = hide;
    this.safelySendToToolbar('hide-address-bar', hide);
    this.setToolbarHeight();
  }

  setToolbarHeight() {
    this.#toolbarHeight = this.#hideToolbar
      ? 0
      : this.#hideAddressBar
        ? store.get('window.toolbarHeight') - this.#addressBarHeight
        : store.get('window.toolbarHeight');
    this.onWindowResize();
  }

  setHideFavicon(hide: boolean) {
    this.#hideFavicon = hide;
    this.safelySendToToolbar('hide-favicon', hide);
  }

  onWindowResize() {
    this.#toolbar.setBounds({
      x: 0,
      y: 0,
      width: this.#window.getContentBounds().width,
      height: this.#toolbarHeight
    });
    const sortedTabs = this.#tabs.toSorted((a, b) => {
      if (a === this.#activeTab) {
        return 1;
      } else if (b === this.#activeTab) {
        return -1;
      } else if (a.giveMeSomeAir && !b.giveMeSomeAir) {
        return 1;
      } else if (b.giveMeSomeAir && !a.giveMeSomeAir) {
        return -1;
      } else {
        return 0;
      }
    });
    let subtrahend = 0;

    sortedTabs.forEach((tab) => {
      tab.setActive();
      log.debug('window', this.id, 'set tabs bounds minus', subtrahend);
      tab.setBounds({
        x: 0,
        y: this.#toolbarHeight,
        width: this.#window.getContentBounds().width - subtrahend,
        height: this.#window.getContentBounds().height - this.#toolbarHeight
      });

      if (tab.giveMeSomeAir) {
        subtrahend++;
      }
    });
    if (this.#windowType === 'normal') {
      store.set('window.width', this.#window.getBounds().width);
      store.set('window.height', this.#window.getBounds().height);
    }
  }

  setActiveTab(tab) {
    this.#activeTab = tab;
    this.safelySendToToolbar('change-tab', {
      action: 'active',
      payload: {
        id: tab.id,
        url: tab.url,
        canGoBack: tab.navigationHistory[0],
        canGoForward: tab.navigationHistory[1]
      }
    });
    tab.setActive();
    this.setTabsBounds();
  }

  setAlwaysOnTop(...args) {
    // @ts-ignore ...
    this.#window.setAlwaysOnTop(...args);
  }

  setSize(width, height) {
    if (process.platform === 'win32') {
      this.#window.setMinimumSize(width, height);
      this.#window.setSize(width, height);
    } else {
      this.#window.setSize(width, height);
    }
  }

  setPosition(x, y) {
    this.#window.setPosition(x, y);
  }

  registerShortcut() {
    globalShortcut.register('CommandOrControl+Shift+U', () => {
      this.setHideAddressBar(!this.#hideAddressBar);
    });
    globalShortcut.register('CommandOrControl+Shift+F', () => {
      this.setHideFavicon(!this.#hideFavicon);
    });
  }

  unregisterShortcut() {
    globalShortcut.unregister('CommandOrControl+Shift+U');
    globalShortcut.unregister('CommandOrControl+Shift+F');
  }

  closeTabs(predicate: (tab: Tab) => boolean) {
    this.#tabs.forEach((tab) => {
      if (predicate(tab)) {
        tab.close();
      }
    });
  }

  close(e) {
    if (this.#tabs.length) {
      e.preventDefault();
      this.#tabs.forEach((tab) => tab.close());
    } else {
      this.#toolbar.webContents.close();
    }
  }

  closed() {
    this.#isClosed = true;
    Window.#windowMap.delete(this.#window);

    if (Window.isAppQuiting && !Window.#windowMap.size) {
      app.quit();
    }
  }
}

import cloneDeep from 'lodash-es/cloneDeep';
import { TimerActivate } from './press-for-payment';
// 设置催付
export const setupReminders = async (
  request,
  agentConfig,
  isSilence,
  userList,
  log,
  setUserList,
  sendMessage
) => {
  if (request.url()?.includes('/backstage/robot/assistant/answerRecommend') && !isSilence) {
    const postData = JSON.parse(request.postData() ?? '{}');
    console.log(postData, 'postDatasetupReminders');
    if (!userList[postData.uid]?.orderInfo?.length) {
      if (postData?.type?.includes(1) && postData?.type?.includes(2)) {
        if (userList[postData.uid]) {
          userList[postData.uid]?.timerActivate?.cancel();
        }
        const { press_interval, press_payment_list, pressImgs } = cloneDeep(agentConfig);
        const orderInfo = userList[postData.uid]?.orderInfo ?? [];
        setUserList(postData.uid, {
          user_id: postData.uid,
          timerActivate: new TimerActivate(
            postData.uid,
            press_interval,
            press_payment_list,
            pressImgs,
            (uid, content, type) => {
              sendMessage(uid, content, type);
            },
            log
          ),
          orderInfo
        });
        userList[postData.uid]?.timerActivate?.start();
      }
    }
  }
};

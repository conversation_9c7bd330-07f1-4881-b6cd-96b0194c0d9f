// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck
import fs from 'fs-extra';
import path from 'path';
import download from 'download';
// import { this.ctx.Debug } from "./debug.js";

const downloadLock: { string: Promise<string> } = {};

/** 通过URL获取图片路径 */
export const getImgPathByUrl = async (url: string, cacheDir: string, log) => {
  if (!url) {
    throw new Error('url must not be empty');
  }
  const cachedImgDir = path.resolve(cacheDir, './preload-imgs');
  fs.ensureDirSync(cachedImgDir);

  const filename = path.basename(url);
  const filePath = path.join(cachedImgDir, filename);

  if (fs.existsSync(filePath)) {
    log.debug('图片已存在', filePath);
    return filePath;
  }

  if (downloadLock[filename]) {
    return downloadLock[filename];
  }

  let resolve;
  downloadLock[filename] = new Promise((res) => (resolve = res));
  log.debug('下载图片', url);
  await download(url, cachedImgDir, {
    filename
  });
  log.debug('下载图片完成', filePath);
  resolve(filePath);
  delete downloadLock[filename];
  return filePath;
};

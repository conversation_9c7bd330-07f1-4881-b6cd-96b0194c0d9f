{"name": "x-browser", "version": "2.5.8", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "muyan.works", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "test": "vitest", "dev:muyan": "concurrently \"npm run start-muyan\" \"npm run dev -- --mode=muyan-browser\"", "start-muyan": "npm --prefix entry_muyan run dev", "build-muyan": "npm --prefix entry_muyan run build", "dev:watch": "electron-vite dev -w", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "build:mac:muyan": "npm run build -- --mode=muyan-browser && electron-builder --mac --config electron-builder-muyan.yml", "build:mac-x64:muyan": "npm run build:mac:muyan -- --x64", "build:win:muyan": "npm run build -- --mode=muyan-browser && electron-builder --win --config electron-builder-muyan.yml", "postbuild:win:muyan": "node build_muyan/set-cos-symlink.mjs", "prepare": "husky", "commit": "czg", "postversion": "git push && git push --tags"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.55.0", "@opentelemetry/exporter-metrics-otlp-proto": "^0.55.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.55.0", "@opentelemetry/sdk-node": "^0.55.0", "@opentelemetry/sdk-trace-node": "^1.28.0", "@opentelemetry/semantic-conventions": "^1.28.0", "@sentry/electron": "^5.7.0", "@sindresorhus/is": "^4.6.0", "ali-oss": "^6.21.0", "aliyun-sdk": "^1.12.10", "axios": "^1.7.7", "bufferutil": "^4.0.8", "builder-util-runtime": "^9.2.10", "cron": "^3.2.1", "delay": "^6.0.0", "download": "^8.0.0", "electron-default-menu": "^1.0.2", "electron-store": "^10.0.0", "electron-updater": "^6.1.7", "ffi-rs": "^1.2.1", "form-data-encoder": "^1.7.2", "got": "^11.8.6", "host-metrics-extend": "^0.35.5", "log4js": "^6.9.1", "mnemonist": "^0.39.8", "openai": "^4.67.3", "p-queue": "^8.0.1", "p-timeout": "^6.1.3", "puppeteer-core": "^23.5.0", "puppeteer-in-electron": "^3.0.5", "socket.io-client": "^4.8.0", "tencentcloud-cls-sdk-js": "^1.0.5", "tokenize-features": "^0.0.3", "unique-random-array": "^4.0.0", "url-regex-safe": "^4.0.0", "utf-8-validate": "^6.0.5"}, "devDependencies": {"@babel/preset-env": "^7.25.7", "@commitlint/cli": "^19.5.0", "@commitlint/config-conventional": "^19.5.0", "@electron-toolkit/eslint-config-prettier": "^2.0.0", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^20.14.8", "@types/puppeteer-core": "^7.0.4", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.11.0", "@typescript-eslint/parser": "^8.11.0", "@vitejs/plugin-react": "^4.3.1", "antd": "^5.21.1", "concurrently": "^9.0.1", "cz-git": "^1.10.1", "czg": "^1.10.1", "electron": "^31.0.2", "electron-builder": "^24.13.3", "electron-publisher-cos": "^1.0.4", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.3", "html-react-parser": "^5.1.18", "husky": "^9.1.6", "less": "^4.2.0", "lodash-es": "^4.17.21", "prettier": "^3.3.2", "react": "^18.3.1", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.3.1", "react-perfect-scrollbar": "^1.5.8", "rollup-plugin-visualizer": "^5.12.0", "simplebar-react": "^3.3.0", "typescript": "^5.5.2", "vite": "^5.3.1", "vite-plugin-bytecode2": "^2.3.0", "vitest": "^3.0.9"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["npm run lint"]}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}
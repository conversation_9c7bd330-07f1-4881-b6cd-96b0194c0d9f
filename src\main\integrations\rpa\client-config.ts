import { Config } from './type';
// import axios from 'axios';
import got from '../../utils/got';

/** 加载配置*/
export const loadConfig = async (url: string, token: string, log) => {
  log.info('loadConfig url', url);

  try {
    const res = await got(url, {
      headers: {
        Authorization: `${token}`
      }
    }).json<any>();

    return [null, JSON.parse(res.data.configContent)];
  } catch (err) {
    log.error('loadConfig error', err);
    return [err];
  }
};

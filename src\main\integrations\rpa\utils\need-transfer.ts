/** 是否需要转人工 */
export const needTransfer = (uid: string, content: string, log, shop?: string, msgId?: string) => {
  // const { systemManager } = ctx;
  const hasKeyword = content.includes('转接人工');
  if (hasKeyword) {
    log.info('关键词转接人工', uid, shop, content, msgId);
    return true;
  }
  const emptyContent = !content.trim();
  if (emptyContent) {
    log.info('空消息转接人工', uid, shop, content, msgId);
    return true;
  }
  if (content.match(/\{.*}/)) {
    log.info('存在未替换变量转人工', uid, shop, content, msgId);
    return true;
  }
  return false;
  // const user = systemManager.getUser(uid);
  // const repeatMsg = user.lastAnswerMsg === content;
  // if (repeatMsg) {
  //   ctx.Debug.transfer("重复消息转接人工", uid);
  //   return true;
  // }
};

import React, { useState } from 'react';
import Menu from './Menu';
import Documentary from './Documentary';
import Transferlabor from './Transferlabor';
import MyShop from './my_shop';
import Product from './my_shop/Product';
const AiSetup: React.FC = () => {
  const [key, setKey] = useState('1');
  const [shopKey, setShopKey] = useState('');
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'start',
        height: '100%'
      }}
    >
      <Menu
        onChange={(val) => {
          setKey(val);
        }}
      />
      <div
        style={{
          // display: "flex",
          height: '100%',
          width: '100%',
          overflowY: 'scroll'
          // flex: 1,
        }}
      >
        {key === '1' && <Transferlabor />}
        {key === '2' && <MyShop setKey={setKey} shopKey={shopKey} setShopKey={setShopKey} />}
        {key === '3' && <Product setKey={setKey} shopName={shopKey} setShopName={setShopKey} />}
        {key === '4' && <Documentary />}
      </div>
    </div>
  );
};

export default AiSetup;

/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import Modal from '@/components/Modal';
import { PlatformInfoEditUploadImg } from '../PlatformInfoEditUploadImg';
import { message, Spin } from 'antd';
import style from './index.module.css';
import { useEffect, useState } from 'react';
import { getAgentium, platformData, platformConfig, updatePlatformConfig } from '@/api/myqaApi';
import cloneDeep from 'lodash-es/cloneDeep';
import PlatformInfoEditGoodSpec from '../PlatformInfoEditGoodSpec';

type PlatformInfo = {
  aiGoodSku: string;
  aiGoodSkuid: string;
  pcode: string;
  img: string;
  aiGoodSpec: string;
  mall_name: string;
  item_id: number;
};
export const ProductEdit: React.FC<{
  data: any;
  title: string;
  open: boolean;
  setOpen: (bool: boolean) => void;
}> = ({ data, title, open, setOpen }) => {
  const [platform_info, setPlatformInfo] = useState<PlatformInfo[]>([]);
  const [editData, setEditData] = useState<any>({});
  const [loading, setLoading] = useState(false);
  const [goodSpecOpen, setGoodSpecOpen] = useState(false);
  const [attribute, setAttribute] = useState<string[]>([]);
  const [platformId, setPlatformId] = useState<string>('');
  const onChange = (value: string): void => {
    updatePlatformConfig(platformId, value).then(() => {
      getMetadata();
      setOpen(false);
      message.success('配置已修改');
      setEditData({});
    });
  };
  useEffect(() => {
    if (open) getMetadata();
  }, [open]);

  const getMetadata = (): void => {
    setLoading(true);
    Promise.all([
      getAgentium({
        page_num: 1,
        page_size: 1,
        assistant_id: localStorage.getItem('assistantId')
      }),
      platformData(),
      platformConfig()
    ]).then((res) => {
      setAttribute(res[0].data?.data?.[0]?.meta_data?.attribute ?? []);
      const platformInfo = diffPlatformInfo(
        res[1].data,
        JSON.stringify(res[2].data[0].meta_data ?? {})
      );
      setPlatformInfo(platformInfo);
      setPlatformId(res[2].data[0].id);
      setEditData(platformInfo.find((item) => item.aiGoodSkuid === data.item_id) ?? {});
      setLoading(false);
    });
  };

  const diffPlatformInfo = (platform_info, value): PlatformInfo[] => {
    const data = JSON.parse(value)?.platform_info ?? [];
    const newArr = cloneDeep(data);
    platform_info.forEach((item) => {
      if (!data.find((plitem) => plitem.aiGoodSkuid === item.aiGoodSkuid)) {
        newArr.push(item);
      }
    });
    return newArr;
  };
  return (
    <Modal
      open={open}
      width={550}
      setOpen={setOpen}
      onCancel={() => {
        setEditData({});
      }}
      onOk={() => {
        const newData = cloneDeep(platform_info);
        onChange(
          newData.map((item) => {
            if (item.aiGoodSkuid === editData.aiGoodSkuid) {
              return editData;
            } else {
              return item;
            }
          })
        );
      }}
      title={title}
    >
      <Spin spinning={loading}>
        <div
          style={{
            padding: '24px'
          }}
        >
          <div
            style={{
              marginBottom: '20px'
            }}
          >
            <span className={style.edit_label}>店铺名称: &nbsp;</span>
            <span>{editData.mall_name}</span>
          </div>
          <div
            style={{
              marginBottom: '20px',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <span className={style.edit_label}>商品名称: &nbsp;</span>
            <span
              style={{
                flex: '1'
              }}
            >
              {editData.aiGoodSku}
            </span>
          </div>
          <div
            style={{
              marginBottom: '20px'
            }}
          >
            <span className={style.edit_label}>商品SKU: &nbsp;</span>
            <span>{editData.aiGoodSkuid}</span>
          </div>
          <div
            style={{
              marginBottom: '20px'
            }}
          >
            <span className={style.edit_label}>商品ID: &nbsp;</span>
            <span>{editData.item_id}</span>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '20px'
            }}
          >
            <div className={style.edit_label}>商品图片: &nbsp;</div>
            {!loading && (
              <PlatformInfoEditUploadImg
                value={editData.img ? [{ url: editData.img, type: 'image/jpeg' }] : []}
                onChange={(value) => {
                  const newData = cloneDeep(editData);
                  newData.img = value?.[0]?.url ?? '';
                  setEditData(newData);
                }}
              />
            )}
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: '20px'
            }}
          >
            <div className={style.edit_label}>商品规格: &nbsp;</div>
            <span
              style={{
                color: '#1890ff',
                cursor: 'pointer',
                flex: '1'
              }}
              onClick={() => {
                setGoodSpecOpen(true);
              }}
            >
              {editData.aiGoodSpec || '点击添加'}
            </span>
          </div>
          <PlatformInfoEditGoodSpec
            setOpen={setGoodSpecOpen}
            open={goodSpecOpen}
            data={attribute}
            value={editData.aiGoodSpec}
            onChange={(val) => {
              const newData = cloneDeep(editData);
              newData.aiGoodSpec = val;
              setEditData(newData);
            }}
          />
        </div>
      </Spin>
    </Modal>
  );
};

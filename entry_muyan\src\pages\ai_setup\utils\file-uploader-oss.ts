import OSS from 'ali-oss';

const client = new OSS({
  region: 'oss-cn-beijing',
  accessKeyId: 'LTAI5tLe2Ae9gxVJVzjpLDEd',
  accessKeySecret: '******************************',
  bucket: 'muyu-public'
});
async function uploadFile(key, file) {
  try {
    const result = await client.put(`/rpa/${key}`, file);
    return result;
  } catch (error) {
    console.log('Error uploading file:', error);
    throw error;
  }
}

export { uploadFile };

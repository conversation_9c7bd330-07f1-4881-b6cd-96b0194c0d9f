import MyQA from './myqa';
import { NewData, Order, ReportMessage, UserGoods } from './type';
import { metrics, trace } from '@opentelemetry/api';
import packageJson, { name, version } from '../../../../package.json';
import _ from 'lodash';
import PQueue from 'p-queue';
import { upload2MyQA } from './utils/upload-chat';
import dayjs from 'dayjs';
import ms from 'ms';
import * as Sentry from '@sentry/electron/main';
// import { store } from '../utils';
import FollowUp from './follow-up';
import { createPromiseCapability } from '../../utils/promise';
import { ReplyMessageStatus } from './chat';
import { is } from '@electron-toolkit/utils';

const tracer = trace.getTracer(name, version);
const collectedMessageCount = metrics.getMeter(packageJson.name).createCounter('muyan-message');
const userCount = metrics.getMeter(packageJson.name).createGauge('muyan-user');

class User {
  #myqa: MyQA;
  #goods: UserGoods = {};
  #msgQueue = new PQueue({ concurrency: 1 });
  transfered: boolean = false;
  upload2MyQA: (data: ReportMessage) => void;
  #nicknames: Set<string>;

  static shopUserCount: Record<string, Set<string>> = {};

  constructor(
    private ctx,
    private user: string,
    private shop?: string
  ) {
    // this.#myqa = new MyQA(ctx, user);
    this.upload2MyQA = this._upload2MyQA();
    if (!User.shopUserCount[shop!]) {
      User.shopUserCount[shop!] = new Set();
    }
    this.#nicknames = new Set();
  }

  addNickname(nickname: string) {
    this.#nicknames.add(nickname);
    return this;
  }

  async addMsgAndRun(
    msgs: Array<NewData>,
    orders?: Array<Order>,
    batch?: number,
    nickname?: string
  ) {
    // @ts-ignore get exist
    const username = this.ctx.env.config.options.username;
    return await tracer.startActiveSpan('request-myqa', async (span) => {
      try {
        if (batch === 1) {
          // 如果是第一批消息，将最后用户消息 firstBatchNeedReply 设置为 true
          for (let i = msgs.length - 1; i >= 0; i--) {
            if (!msgs[i].metadata.isCS) {
              msgs[i].metadata.firstBatchNeedReply = true;
            } else {
              break;
            }
          }
        }
        for (let i = 0; i < msgs.length; i++) {
          const msg = msgs[i];
          if (dayjs().diff(dayjs(msg.time), 'y') > 0) {
            continue;
          }

          collectedMessageCount.add(1, {
            mode: import.meta.env.MODE,
            version: packageJson.version,
            instance: this.ctx.env.clientId,
            loggedUser: username,
            // env: is.dev ? 'development' : 'production',
            shop: this.shop,
            cs: msg.metadata.cs,
            uid: this.user,
            nick: nickname,
            type: msg.metadata.isCS ? 'cs' : 'user'
          });
          // if (!msg.id) {
          //   msg.id = "no_msg_id";
          // }
          if (msg.metadata.isCS) {
            // 静默模式下存在客服消息
            // 241112所有模式下都有客服消息
            this.ctx.log.info(
              'rpa:receive 客服消息',
              this.user,
              msg.time,
              msg.user,
              msg.type,
              '运行中',
              msg.msg,
              msg.id
            );
            this.#msgQueue.add(() => {
              try {
                this.ctx.chatService.addMessage(
                  msg,
                  {},
                  [_.omit(msg, 'metadata')],
                  [],
                  'customer',
                  this.user,
                  this.shop,
                  batch,
                  nickname
                );
              } catch (e) {
                this.ctx.log.error('msgQueue task run failed2', this.user, e);
              }
            });
            continue;
          }
          // 用户消息和系统消息
          const originMessages = [
            {
              ..._.omit(msg, 'metadata'),
              userOrdersCnt: orders?.filter((order) => order.orderStatus !== '已关闭').length ?? 0
            }
          ];
          this.ctx.log.info(
            'rpa:receive 收到消息',
            msg.user === 'sys' ? this.user + ' sys' : msg.user,
            msg.time,
            msg.type,
            '运行中',
            msg.msg,
            msg.id
          );
          if (msg.user !== 'sys') {
            this.transfered = false;
            User.shopUserCount[this.shop!].add(this.user);
            userCount.record(User.shopUserCount[this.shop!].size, {
              mode: import.meta.env.MODE,
              version: packageJson.version,
              instance: this.ctx.env.clientId,
              loggedUser: username,
              // env: is.dev ? 'development' : 'production',
              shop: this.shop
            });
            this.ctx.emit('user-count', this.shop, User.shopUserCount[this.shop!].size);
            // Sentry.captureMessage('qianniu receive', {
            //   extra: {
            //     ...msg
            //   },
            //   tags: {
            //     username,
            //     receiveuser: msg.user,
            //     receivemsg: msg.msg
            //   },
            //   level: 'warning'
            // });
            this.ctx.followUp?.addMessage(msg.metadata.shop, msg.user, msg.msg);
          }
          const j = i;

          // while (++j < msgs.length) {
          //   const nextMsg = msgs[j];
          //
          //   if (
          //     nextMsg.user === this.user &&
          //     nextMsg.type === "txt" &&
          //     nextMsg.msg.length <= 2
          //   ) {
          //     this.ctx.log.info(
          //       "rpa:receive 收到消息",
          //       nextMsg.user,
          //       nextMsg.time,
          //       nextMsg.type,
          //       "运行中",
          //       nextMsg.msg,
          //       nextMsg.id,
          //     );
          //     Sentry.captureMessage("qianniu receive", {
          //       extra: {
          //         ...nextMsg,
          //       },
          //       tags: {
          //         username,
          //       },
          //       level: "warning",
          //     });
          //     this.ctx.log.debug("消息被合并", nextMsg.user, nextMsg);
          //     msg.msg += nextMsg.msg;
          //     originMessages.push(_.omit(nextMsg, "metadata"));
          //     i = j;
          //   } else {
          //     break;
          //   }
          // }

          this.extractGoods(msg);
          const goods = _.cloneDeep(this.#goods);
          Object.assign(originMessages[0], {
            info: {
              data: {
                goods_name: this.#goods.name,
                goods_spec: this.#goods.skuName
              }
            }
          });
          this.ctx.log.debug(
            '加入请求myqa队列',
            msg.user === 'sys' ? this.user + ' sys' : msg.user,
            msg.msg,
            msg.id,
            '前面还有',
            this.#msgQueue.size + this.#msgQueue.pending,
            '个请求等待处理'
          );
          this.#msgQueue.add(
            async () => {
              try {
                const start = +new Date();
                this.ctx.chatService.addMessage(
                  msg,
                  goods,
                  originMessages,
                  orders,
                  msg.user === 'sys' ? 'sys' : 'user',
                  this.user,
                  this.shop,
                  batch,
                  nickname
                );
              } catch (e) {
                this.ctx.log.error('msgQueue task run failed', this.user, e);
              }
            }
            // { priority: 1 },
          );
        }
        // return await this.#myqa.run();
      } catch (e) {
        this.ctx.log.error('addMsgAndRun error', this.user, e);
        this.ctx.emit('myqa-error', this.user, e);
      } finally {
        span.end();
      }
    });
  }

  reply(content, msgId, extra = {}) {
    if (!this.#nicknames.size) {
      this.ctx.log.error('user has no nickname, stop replying', this.user, content, msgId);
      return;
    }
    const last = [...this.#nicknames][this.#nicknames.size - 1];
    this.ctx.emit('response-to-user', last, content, {
      msgId: msgId,
      shop: this.shop,
      uid: this.user,
      ...extra
    });
  }

  welcome() {
    const { greeting } = this.ctx.agentConfig;

    if (greeting) {
      // 确保rpa:receive 收到消息 已打印
      process.nextTick(() => {
        this.reply(
          [
            {
              type: 'text',
              text: [
                {
                  value: greeting
                }
              ]
            }
          ],
          'greeting_' + this.user
        );
      });
    }
  }

  preSale(text?: string, imagePath?: string, orderLink?: string, tips?: string) {
    const _text = text || this.ctx.agentConfig.recommendation;
    const _image = imagePath || this.ctx.agentConfig.picture_info_path;
    // 图片
    // 文字
    // 下单链接
    // 0.01元
    // 确保rpa:receive 收到消息 已打印
    process.nextTick(() => {
      if (_image) {
        this.reply(
          [
            {
              type: 'picture',
              text: [
                {
                  value: _image
                }
              ]
            }
          ],
          'preSale-image_' + this.user
        );
      }
      if (_text) {
        this.reply(
          [
            {
              type: 'text',
              text: [
                {
                  value: _text
                }
              ]
            }
          ],
          'preSale-text_' + this.user
        );
      }
    });
  }

  checkOrders() {
    const promiseCapability = createPromiseCapability();
    this.ctx.emit('grab-orders', this.user, this.shop, promiseCapability);

    return promiseCapability.promise;
  }

  private _upload2MyQA() {
    let list: ReportMessage[] = [];
    const upload = _.throttle(
      () => {
        upload2MyQA(list, this.ctx);
        list = [];
      },
      ms('1m'),
      { leading: false }
    );

    return (data) => {
      list.push(data);
      upload();
    };
  }

  private extractGoods(msg: NewData) {
    const goodsName = msg.metadata.goods?.name;
    const skuName = msg.metadata.goods?.skuName;
    if (goodsName) {
      this.#goods.name = goodsName;
      this.#goods.skuName = skuName;
      this.#goods.url = undefined;
      this.#goods.id = undefined;
    }
    // if (msg.type === 'card') {
    //   try {
    //     const goodsLink = new URL(msg.msg);
    //     this.#goods.url = msg.msg;
    //     this.#goods.id = goodsLink.searchParams.get('id') as string;
    //     this.#goods.name = undefined;
    //     this.#goods.skuName = undefined;
    //   } catch {
    //     return false;
    //   }
    // }
  }
}

export default User;

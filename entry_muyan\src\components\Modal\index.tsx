import React from 'react';
import { Modal, Button } from 'antd';
import style from './index.module.css';
type Props = {
  open: boolean;
  footer?: boolean;
  children?: React.ReactNode;
  setOpen: (open: boolean) => void;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  width?: number | string;
  okText?: string;
  cancelText?: string;
  top?: string;
  okLoading?: boolean;
};
const App: React.FC<Props> = ({
  onOk,
  onCancel,
  title,
  open,
  setOpen,
  children,
  footer = true,
  width = 600,
  okText = '确定',
  cancelText = '取消',
  top = '100px',
  okLoading = false
}) => {
  const handleOk = (): void => {
    onOk && onOk();
  };

  const handleCancel = (): void => {
    setOpen(false);
    onCancel && onCancel();
  };
  return (
    <Modal
      title={title}
      onCancel={handleCancel}
      style={{
        top: top
      }}
      styles={{
        content: { padding: 0 }
      }}
      open={open}
      footer={null}
      width={width}
    >
      <div>{children}</div>
      {footer && (
        <div className={style.footer}>
          <Button onClick={handleCancel} className={style.button_mr}>
            {cancelText}
          </Button>
          <Button loading={okLoading} type="primary" onClick={handleOk}>
            {okText}
          </Button>
        </div>
      )}
    </Modal>
  );
};

export default App;

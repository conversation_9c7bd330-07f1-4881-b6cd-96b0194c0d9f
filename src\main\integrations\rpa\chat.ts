import OpenAI from 'openai';
import _ from 'lodash';
import { Logger } from 'log4js';
import { NewData, Order, UserGoods } from './type';
import dayjs from 'dayjs';
import pTimeout from 'p-timeout';
import ms from 'ms';
import { io } from 'socket.io-client';
import { createPromiseCapability } from '../../utils/promise';

class Chat {
  #socket: ReturnType<typeof io>;

  #openai: OpenAI;
  #assistantId: string;
  #threadId?: string;
  #createThreadPromise?: ReturnType<typeof createPromiseCapability>;
  #getThreadPromise: ReturnType<typeof createPromiseCapability<string>>;
  #log: Logger;

  constructor(private ctx) {
    const server = ctx.env.host;
    const auth = ctx.env.accessToken;
    ctx.log.info('chat server', server);
    this.#socket = io(server, {
      retries: 3,
      ackTimeout: 10000,
      transports: ['websocket'],
      extraHeaders: {
        Authorization: auth,
        'x-client-id': ctx.env.clientId
      }
    });
    this.handleEvents();

    this.#assistantId = ctx.env.assistantId;
    this.#log = ctx.log;
    // const baseURL = ctx.env.endpoint;
    // const apiKey = ctx.env.secretKey;
    // this.#openai = new OpenAI({
    //   apiKey,
    //   baseURL,
    //   maxRetries: 4,
    // });
    this.#getThreadPromise = createPromiseCapability();
  }

  public getThreadId() {
    return this.#getThreadPromise.promise;
  }

  handleEvents() {
    this.#socket.on('connect', () => {
      this.ctx.log.info('chat connected');
    });
    this.#socket.on('disconnect', (reason) => {
      this.ctx.log.warn('chat disconnected', this.#socket.active, reason);
    });
    this.#socket.on('connect_error', (err) => {
      this.ctx.log.error('chat socket error', this.#socket.active, err);
    });
    this.#socket.on('reply-message', (data, callback) => {
      this.ctx.log.debug('reply-message', data);
      this.ctx
        .userManager!.get(data.tbUid, data.mallId)
        .reply(JSON.parse(data.content), data.msgId, {
          backendId: data.messageId,
          userStatus: this.getUserStatus(data.procState),
          question: data.question,
          transferReason: data.transferReason
          // question: [{ questionContent: '[{"text": [{"value": "666", "meta_data": null}], "type": "text"}]', questionRealAt: '1735873312000' }, { questionContent: '[{"text": [{"value": "777", "meta_data": null}], "type": "text"}]', questionRealAt: '1735873366000' }]
        });
      callback(+new Date());
    });
    this.#socket.on('batch-push-message', (data, callback) => {
      this.ctx.log.debug('batch-push-message', data);
      this.ctx
        .userManager!.get(data.tbUid, data.mallId, false)
        .addNickname(data.uid)
        .reply(JSON.parse(data.content), data.msgId, {
          backendId: data.messageId,
          userStatus: this.getUserStatus(data.procState),
          question: data.question,
          transferReason: data.transferReason,
          ignoreTransferred: data.ignoreTransferred ?? true
          // question: [{ questionContent: '[{"text": [{"value": "666", "meta_data": null}], "type": "text"}]', questionRealAt: '1735873312000' }, { questionContent: '[{"text": [{"value": "777", "meta_data": null}], "type": "text"}]', questionRealAt: '1735873366000' }]
        });
      callback(+new Date());
    });
    this.#socket.on('grab-orders', async (data, callback) => {
      this.ctx.log.debug('grab-orders', data);
      const { mallId, cs, uids } = data;
      const o: { uid: string; mallId: string; userOrders: Order[] }[] = [];
      for (const uid of uids) {
        const orders = await this.ctx.orderService?.getOrders(cs, uid);

        o.push({
          uid,
          mallId,
          userOrders: orders.map((order) => {
            const { orderStatus, payStatus, shippingStatus } = this.processOrder(order);
            return {
              orderSn: order.orderId,
              orderStatus,
              payStatus,
              shippingStatus,
              orderTime: dayjs(order.orderTime).unix(),
              orderGoodsList: order.goods.map((goods) => {
                return {
                  goodsId: goods.id,
                  goodsName: goods.name,
                  skuId: goods.skuId,
                  spec: goods.skuName
                };
              })
            };
          })
        });
      }
      this.ctx.log.debug('grab-orders-update', o);
      this.#socket.emit('upload-grab-orders-data', {
        grabOrderDTOS: o
      });
    });
    this.#socket.on('notify', (data, callback) => {
      const { action } = data;
      switch (action) {
        case 'post-product':
          this.ctx.grabGoods?.start(true, [data.data.mallId]);
          break;
        default:
          this.ctx.log.error('notify unknown action', data);
      }
    });
  }

  addMessage(
    msg: NewData,
    goods: UserGoods,
    originMessages: any[],
    orders?: Order[],
    role?: string,
    user?: string,
    shop?: string,
    batch?: number,
    nickname?: string
  ) {
    // const shop = _.get(msg, "metadata.shop");
    // await this.createThreadWrap(shop);
    // this.#log.debug("chat addMessage", user, shop, msg, goods, msg.id);
    let content;

    switch (msg.type) {
      case 'txt':
      case 'link':
        content = [
          {
            type: 'text',
            text: [
              {
                value: msg.msg,
                meta_data: null
              }
            ]
          }
        ];
        break;
      case 'image':
        content = [
          {
            type: 'pic',
            text: [
              {
                value: msg.metadata.image?.txt.replace(/\r|\n/g, '') || msg.msg,
                meta_data: {
                  pic: {
                    value: msg.metadata.image?.url
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'card':
        content = [
          {
            type: 'card',
            text: [
              {
                value: msg.msg,
                meta_data: null
              }
            ]
          }
        ];
        break;
      case 'emoji':
        content = [
          {
            type: 'emoji',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  emoji: {
                    value: msg.msg
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'quote':
        content = [
          {
            type: 'quote',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  ...msg.metadata.quoteMsg,
                  quotation: {
                    value: msg.metadata.quoteMsg?.msg
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'transfer':
        content = [
          {
            type: 'text',
            text: [
              {
                value: msg.msg,
                meta_data: null
              }
            ]
          }
        ];
        break;
      case 'audio':
        content = [
          {
            type: 'audio',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  audio: {
                    value: msg.metadata.audio?.url
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'video':
        content = [
          {
            type: 'video',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  video: {
                    value: msg.metadata.video?.url
                  }
                }
              }
            ]
          }
        ];
        break;
      default:
        this.#log.error('Unsupported type', user, msg);
        this.ctx.feishu
          ?.send(
            '消息异常',
            `- **报警原因**：未知消息类型
- **报警优先级**：${msg.metadata.isCS ? '低' : '🔴高'}
- **详情**：
  - **店铺**：${shop}
  - **用户**：${nickname}
  - **uid**：${user}
  - **msgId**：${msg.id}
  - **是否用户消息**：${msg.metadata.isCS ? '否' : '是'}`
          )
          .then((res) => {
            this.ctx.log!.info('报警结果_chat', res);
          });
        throw new Error('Unsupported type ' + msg.type);
    }

    const metadata = {
      // ...item.metadata,
      // assistant_id: this.#assistantId,
      uid: user,
      // thread_id: this.#threadId,
      originMessages,
      goods: {
        item_id: goods.id,
        url: goods.url,
        title: goods.name
      },
      userOrders: orders?.map((order) => {
        const { orderStatus, payStatus, shippingStatus } = this.processOrder(order);
        return {
          orderSn: order.orderId,
          orderStatus,
          payStatus,
          shippingStatus,
          orderTime: dayjs(order.orderTime).unix(),
          orderGoodsList: order.goods.map((goods) => {
            return {
              goodsId: goods.id,
              goodsName: goods.name,
              skuId: goods.skuId,
              spec: goods.skuName
            };
          })
        };
      })
    };

    const args = {
      mallId: shop,
      uid: nickname,
      role: role,
      tbUid: user,
      ...(role === 'customer' ? { customerName: msg.user } : {}),
      content: JSON.stringify(content),
      metadata: metadata,
      source: this.ctx.env.isSilence ? 'agent' : this.ctx.env.isAssistant ? 'assist' : 'open',
      messageRealAt: +dayjs(msg.time),
      msgId: msg.id,
      to: msg.to,
      cs: msg.metadata.cs,
      batch,
      firstBatchNeedReply: msg.metadata.firstBatchNeedReply,
      aiAgentConfigId: this.ctx.env.aiAgentConfigId
    };

    this.#log.debug('send-message', args);
    this.#socket.emit('send-message', args);
  }

  processOrder(order: Order) {
    if (this.ctx.platform === 'qianniu') {
      let orderStatus, payStatus, shippingStatus;
      switch (order.orderStatus) {
        case '未付款':
          orderStatus = 1;
          payStatus = 0;
          shippingStatus = 0;
          break;
        case '未付尾款':
          orderStatus = 1;
          payStatus = 10;
          shippingStatus = 0;
          break;
        case '待发货':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 0;
          break;
        case '待收货':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 1;
          break;
        case '未评价':
        case '已评价':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 2;
          break;
        case '已关闭-已付款':
          orderStatus = 2;
          payStatus = 1;
          shippingStatus = 0;
          break;
        case '已关闭':
          orderStatus = 2;
          payStatus = 0;
          shippingStatus = 0;
          break;
      }
      return { orderStatus, payStatus, shippingStatus };
    }
    return {};
  }

  reply(id: string, status: ReplyMessageStatus, time?: number) {
    this.#log.debug('assistant-message', id, status, time);
    this.#socket.emit('assistant-message', {
      messageId: id,
      status,
      messageRealAt: time
    });
  }

  refuseMessage(
    shop: string,
    user: string,
    uid: number | undefined,
    messageId: string,
    message: string,
    reason: 'duplicated' | 'forbidden' | 'duplicated-question',
    extra: any = {}
  ) {
    this.#log.debug('message-refuse', shop, user, uid, messageId, message, reason, extra);
    this.#socket.emit('message-refuse', {
      mallId: shop,
      uid: user, // 用户名
      tbUid: String(uid), // uid
      messageId: messageId,
      message: message, // 消息
      reason: reason, // 驳回原因
      ext: extra // 额外信息
    });
  }

  updateUserStatus(shop: string, user: string, uid: number | undefined, status: boolean) {
    this.#log.debug('inform-transfer-status', shop, user, uid, status);
    this.#socket.emit('inform-transfer-status', {
      mallId: shop,
      uid: user,
      ...(uid ? { tbUid: String(uid) } : {}),
      status: status ? 'complated' : 'failed'
    });
  }

  reportFillTime(id: string, time?: number) {
    this.#log.debug('inform-fill-completed-at', id, time);
    this.#socket.emit('inform-fill-completed-at', {
      messageId: id,
      messageRealAt: time
    });
  }

  getUserStatus(procStatus: string): UserStatus | undefined {
    if (procStatus === '售前') {
      return 'preSale';
    }
    if (procStatus === '售中') {
      return 'duringSale';
    }
    if (procStatus === '售后') {
      return 'afterSale';
    }
  }

  close() {
    this.#socket.disconnect();
  }

  // async run() {
  //   // await this.createThreadWrap();
  //   const run = await pTimeout(
  //     this.#openai.beta.threads.runs.createAndPoll(this.#threadId!, {
  //       assistant_id: this.#assistantId,
  //       // instructions: "Please address the user as Jane Doe. The user has a premium account."
  //     }),
  //     {
  //       milliseconds: ms("10m"),
  //     },
  //   );
  //
  //   if (run.status === "completed") {
  //     const messages = await this.#openai.beta.threads.messages.list(
  //       this.#threadId!,
  //       {
  //         limit: 1,
  //       },
  //     );
  //     const msg = _.get(messages, "data[0]", {});
  //
  //     if (msg.role !== "assistant") {
  //       this.#log.error(
  //         "last message is not from assistant",
  //         this.user,
  //         messages,
  //       );
  //       throw new Error("last message is not from assistant");
  //     }
  //     this.#log.debug("myqa run result", this.user, _.omit(msg, "metadata"));
  //
  //     return msg;
  //   } else {
  //     throw new Error("run status is " + run.status);
  //   }
  // }
}

export default Chat;

export enum ReplyMessageStatus {
  Success = 1,
  DuplicateMessageTransferFailed = 2,
  DuplicateMessageTransferSuccess = 3,
  UserNotFound = 4,
  TransferFailed = 5,
  TransferSuccess = 6,
  UserHasBeenTransfered = 7,
  DuplicateMessage = 8,
  DuplicateQuestion = 9,
  UnknownFailure = 10
}

export type UserStatus = 'preSale' | 'duringSale' | 'afterSale';

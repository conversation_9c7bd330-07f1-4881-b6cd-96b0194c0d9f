import ReactDOM from 'react-dom/client';
import React from 'react';
import Router from './router/index';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import * as Sentry from '@sentry/react';
import packageJson from '../package.json';

dayjs.locale('zh-cn');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Router />
  </React.StrictMode>
);

// Remove Preload scripts loading
postMessage({ payload: 'removeLoading' }, '*');

import.meta.env.PROD &&
  (function () {
    Sentry.init({
      dsn: 'https://<EMAIL>/3',
      environment: 'production',
      release: packageJson.version,
      integrations: [Sentry.replayIntegration(), Sentry.browserTracingIntegration()],

      // Set tracesSampleRate to 1.0 to capture 100%
      // of transactions for tracing.
      tracesSampleRate: 1.0,

      // Set `tracePropagationTargets` to control for which URLs trace propagation should be enabled
      // tracePropagationTargets: [/^\//, /^https:\/\/yourserver\.io\/api/],

      // Capture Replay for 10% of all sessions,
      // plus for 100% of sessions with an error
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0
    });
  })();

// Use contextBridge
// window.ipcRenderer.on('main-process-message', (_event, message) => {
//   console.log(message);
// });

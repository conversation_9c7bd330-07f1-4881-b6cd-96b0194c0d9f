export const getSessionMessage = async (page) => {
  await page.evaluate(() => {
    (window as any).shouldClick = true;
    setInterval(() => {
      if ((window as any).shouldClick) {
        if ((window as any).agentStatue === 'running') {
          const pigeonChatScrollBox = document.querySelector('.pigeonChatScrollBox');
          if (pigeonChatScrollBox) {
            const childElements = pigeonChatScrollBox.children;
            for (let i = 0; i < childElements.length; i++) {
              if (childElements[i].classList.contains('auxo-dropdown-trigger')) {
                const textArr = (childElements[i] as HTMLDivElement).innerText.split('\n');
                if (textArr.length >= 5 && textArr.at(-1) === '<30秒') {
                  (childElements[i] as HTMLDivElement).click();
                  break;
                }
              }
            }
          }
        }
      }
    }, 2000);
  });
};

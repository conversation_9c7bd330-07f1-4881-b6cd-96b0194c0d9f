export const agnetInfo = (version, startAt): SystemStatus => ({
  version: {
    key: '版本信息',
    label: 'version',
    value: version
  },
  startAt: {
    key: '启动时间',
    label: 'startAt',
    value: startAt,
    type: 'date'
  },
  uptime: {
    key: '运行时间',
    label: 'uptime',
    value: 0,
    type: 'time'
  },
  pausedCount: {
    key: '暂停次数',
    label: 'pausedCount',
    value: 0,
    total: 0
  },
  totalReceiveMsgCount: {
    key: '收到消息总数',
    label: 'totalReceiveMsgCount',
    value: 0,
    total: 0
  },
  sendMsgCount: {
    key: '发送消息总数',
    label: 'sendMsgCount',
    value: 0,
    total: 0
  },
  transferCount: {
    key: '转接人数',
    label: 'transferCount',
    value: 0,
    total: 0
  },
  senceCount: {
    key: '触发敏感词总数',
    label: 'senceCount',
    value: 0,
    total: 0
  },
  myqaAnswered: {
    key: 'myqa回复的消息',
    label: 'myqaAnswered',
    value: 0,
    total: 0
  },
  myqaError: {
    key: 'myqa请求失败的消息',
    label: 'myqaError',
    value: 0,
    total: 0
  },
  myqaTimeout: {
    key: 'myqa超时的消息',
    label: 'myqaTimeout',
    value: 0,
    total: 0
  },
  minMyqaElapsed: {
    key: 'myqa最小耗时',
    label: 'minMyqaElapsed',
    value: 0,
    total: 0,
    type: 'time'
  },
  maxMyqaElapsed: {
    key: 'myqa最大耗时',
    label: 'maxMyqaElapsed',
    value: 0,
    total: 0,
    type: 'time'
  },
  avgMyqaElapsed: {
    key: 'myqa平均耗时',
    label: 'avgMyqaElapsed',
    value: 0,
    total: 0,
    type: 'time'
  },
  startupCount: {
    key: '启动次数',
    label: 'startupCount',
    value: 0,
    total: 0
  },
  unhandledRejectionCount: {
    key: '崩溃次数',
    label: 'unhandledRejectionCount',
    value: 0,
    total: 0
  }
});

type StatusInfo = {
  key: string;
  label: string;
  value: string | number;
  type?: 'date' | 'time'; // 可选属性，仅适用于日期和时间类型
  total?: number; // 可选属性，仅适用于需要累积总数的字段
};

export type SystemStatus = {
  version: StatusInfo;
  startAt: StatusInfo;
  uptime: StatusInfo;
  pausedCount: StatusInfo;
  totalReceiveMsgCount: StatusInfo;
  sendMsgCount: StatusInfo;
  transferCount: StatusInfo;
  senceCount: StatusInfo;
  myqaAnswered: StatusInfo;
  myqaError: StatusInfo;
  myqaTimeout: StatusInfo;
  minMyqaElapsed: StatusInfo;
  maxMyqaElapsed: StatusInfo;
  avgMyqaElapsed: StatusInfo;
  startupCount: StatusInfo;
  unhandledRejectionCount: StatusInfo;
};
export class SystemStatusManager {
  public status: SystemStatus;
  private isPaused: boolean;
  private startTime: number;
  private pauseStartTime: number | null;
  private totalPausedTime: number;
  private intervalId: NodeJS.Timeout | null;

  constructor(version, startAt) {
    this.status = agnetInfo(version, startAt);
    this.isPaused = false;
    this.startTime = Date.now();
    this.pauseStartTime = null;
    this.totalPausedTime = 0;
    this.startUptimeTimer();
  }

  private startUptimeTimer() {
    this.intervalId = setInterval(() => {
      if (!this.isPaused) {
        this.updateUptime();
      }
    }, 1000); // 每秒更新一次
  }

  private updateUptime() {
    const currentTime = Date.now();
    const elapsedTime = currentTime - this.startTime - this.totalPausedTime;
    this.status.uptime.value = elapsedTime;
  }

  public start() {
    if (this.isPaused) {
      this.isPaused = false;
      if (typeof this.status.startupCount.value === 'number') {
        this.status.startupCount.value += 1;
      }
      if (this.pauseStartTime) {
        this.totalPausedTime += Date.now() - this.pauseStartTime;
        this.pauseStartTime = null;
      }
    }
  }

  public pause() {
    if (!this.isPaused) {
      if (typeof this.status.pausedCount.value === 'number') {
        this.status.pausedCount.value += 1;
      }
      this.isPaused = true;
      this.pauseStartTime = Date.now();
    }
  }

  public getStatus(): SystemStatus {
    this.updateUptime();
    return this.status;
  }

  // 设置最小耗时
  public setMinMyqaElapsed(value: number) {
    const minValue = this.status.minMyqaElapsed.value;
    if (typeof minValue === 'number') {
      this.status.minMyqaElapsed.value = Math.max(minValue, value);
    }
  }

  // 设置最大耗时
  public setMaxMyqaElapsed(value: number) {
    const maxValue = this.status.maxMyqaElapsed.value;
    if (typeof maxValue === 'number') {
      this.status.maxMyqaElapsed.value = Math.max(maxValue, value);
    }
  }

  // 设置平均耗时
  public setAvgMyqaElapsed(value: number) {
    if (typeof this.status.avgMyqaElapsed.value === 'number') {
      this.status.avgMyqaElapsed.value = value;
    }
  }

  // 累计收消息数
  public addTotalReceiveMsgCount() {
    if (typeof this.status.totalReceiveMsgCount.value === 'number') {
      this.status.totalReceiveMsgCount.value += 1;
    }
  }

  // 累计发消息数
  public addSendMsgCount() {
    if (typeof this.status.sendMsgCount.value === 'number') {
      this.status.sendMsgCount.value += 1;
    }
  }
  // 累计转人工次数
  public addTransferCount() {
    if (typeof this.status.transferCount.value === 'number') {
      this.status.transferCount.value += 1;
    }
  }

  //累计敏感词次数
  public addSenceCount() {
    if (typeof this.status.senceCount.value === 'number') {
      this.status.senceCount.value += 1;
    }
  }

  // 累计myqa回复的消息
  public addMyqaAnswered() {
    if (typeof this.status.myqaAnswered.value === 'number') {
      this.status.myqaAnswered.value += 1;
    }
  }

  // 累计崩溃次数
  public addUnhandledRejectionCount() {
    if (typeof this.status.unhandledRejectionCount.value === 'number') {
      this.status.unhandledRejectionCount.value += 1;
    }
  }

  // 累计myqa请求错误
  public addMyqaError() {
    if (typeof this.status.myqaError.value === 'number') {
      this.status.myqaError.value += 1;
    }
  }

  // 累计myqa请求超时次数
  public addmyqaTimeout() {
    if (typeof this.status.myqaTimeout.value === 'number') {
      this.status.myqaTimeout.value += 1;
    }
  }
}

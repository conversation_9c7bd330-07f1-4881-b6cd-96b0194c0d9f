/* eslint-disable no-case-declarations */
import axios from 'axios';
import { createHash } from 'crypto';
import _ from 'lodash';
import { Logger } from 'log4js';

interface TrackingData {
  message: string;
  nu: string;
  ischeck: string;
  condition: string;
  com: string;
  status: string;
  state: string;
  data: TrackingDetail[];
}

interface TrackingDetail {
  time: string;
  ftime: string;
  context: string;
}

enum LogisticsStatus {
  COLLECTION = 1, // 揽收
  IN_TRANSIT = 0, // 在途
  DELIVERY = 5, // 派件
  SIGNATURE = 3, // 签收
  RETURN = 6, // 退回
  REJECTION = 4, // 退签
  TRANSFER = 7, // 转投
  DOUBTFUL = 2, // 疑难
  CLEARANCE = 8, // 清关
  REFUSAL = 14 // 拒签
}

interface Kuaidi {
  customer: string;
  key: string;
  template_name: string;
  queryUrl: string;
}

class ExpressTracker {
  kuaidi: Kuaidi;
  log: Logger;
  constructor(kuaidi: <PERSON><PERSON><PERSON>, log) {
    this.kuaidi = kuaidi;
    this.log = log;
  }

  // 更新快递配置
  public updateKuaidi(kuaidi: <PERSON><PERSON><PERSON>) {
    this.kuaidi = kuaidi;
  }

  // MD5加密函数，用于生成签名
  public getSignature(param: string, key: string, customer: string) {
    return createHash('md5')
      .update(param + key + customer)
      .digest('hex')
      .toUpperCase();
  }
  /** 格式化快递物流信息 */
  public formatKuadiContent = async (
    uid: string,
    content: string,
    kedangOrder: {
      code: string; // 快递单号
      phone: string; // 收货人手机号
    }
  ) => {
    if (!content) {
      return content;
    }

    if (!uid) {
      return content;
    }

    const templateNameReg = new RegExp(this.kuaidi.template_name);
    if (!templateNameReg.test(content)) {
      return content;
    }
    this.log.debug('匹配到快递模板，开始查询快递信息', uid, content);

    if (!kedangOrder) {
      this.log.debug('未找到包含快递单号的订单', uid);
      return '转接人工';
    }
    let com = '';
    if (kedangOrder.code?.startsWith('JD')) {
      com = 'jd';
    } else if (kedangOrder.code?.startsWith('SF')) {
      com = 'shunfeng';
    } else {
      this.log.debug('未支持的快递公司查询', uid, kedangOrder.code);
      return '转接人工';
    }
    const info = await this.queryExpress({
      com,
      num: kedangOrder.code,
      phone: kedangOrder.phone
    });

    if (!info) {
      this.log.debug('查询快递信息失败', uid, kedangOrder.code);
      return '转接人工';
    }
    this.log.debug('查询快递信息成功', uid, info);
    const { state } = info;
    switch (+state) {
      case LogisticsStatus.COLLECTION:
      case LogisticsStatus.IN_TRANSIT:
      case LogisticsStatus.DELIVERY:
      case LogisticsStatus.SIGNATURE:
        let newData = _.first(info.data)?.context;
        if (!newData) {
          this.log.debug('未找到最新快递信息', uid, info);
          return '转接人工';
        }
        if (/\d{11}/.test(newData)) {
          // 存在手机号，增加话术
          newData += '\n快递已经派送当中，建议您主动与快递小哥沟通协商签收时间呢~~';
        }
        const newContent = content.replace(
          templateNameReg,
          `
快递单号：${kedangOrder.code}
最新物流：${newData}`.trim()
        );
        this.log.debug('格式化快递信息成功', uid, newContent);
        return newContent;

      default:
        this.log.debug('未匹配到快递状态, 转接人工', uid, state);
        return '转接人工';
    }
  };

  // 快递查询方法
  public async queryExpress(param: { com: string; num: string; phone: string }) {
    if (!this.kuaidi) {
      this.log.error('快递配置不存在');
      return;
    }
    // 构建请求参数
    const sign = this.getSignature(JSON.stringify(param), this.kuaidi.key, this.kuaidi.customer);
    const body = {
      customer: this.kuaidi.customer,
      sign,
      param: JSON.stringify(param)
    };

    try {
      // 发送请求
      const response = await axios.post(
        this.kuaidi.queryUrl,
        new URLSearchParams(body).toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      );

      // 解析响应
      const data = response.data;

      return data as TrackingData;
    } catch (error) {
      this.log.error('请求过程中发生错误：', error);
      throw error;
    }
  }
}

export default ExpressTracker;
export type { TrackingData, TrackingDetail, LogisticsStatus, Kuaidi };

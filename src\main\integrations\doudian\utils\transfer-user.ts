export const transferUser = (page, agentStatue) => {
  return page.evaluate((transfer_blacklist) => {
    const conversation: HTMLDivElement | null = document.querySelector(
      "span[data-qa-id='qa-transfer-conversation']"
    );
    if (conversation) {
      conversation.click();
      const customer = document.querySelector(
        "div[style='position: relative; height: 182px; width: 254px; overflow: auto; will-change: transform; direction: ltr;']"
      );
      if (customer) {
        const userList: any = customer.children;
        for (let index = 0; index < userList.length; index++) {
          if (!transfer_blacklist.includes(userList[index]?.innerText?.split('\n')[0])) {
            userList[index]?.children?.[0]?.children?.[0]?.click();
            return;
          }
        }
        return '过滤黑名单后，客服列表为空';
      } else {
        return '未找到用户元素，无法转发';
      }
    } else {
      return '未找到转人工元素，无法转发';
    }
  }, agentStatue.transfer_blacklist);
};

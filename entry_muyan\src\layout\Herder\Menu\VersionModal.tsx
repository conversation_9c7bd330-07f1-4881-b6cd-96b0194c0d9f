// import { useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import logo from '@/assets/imgs/logo.png';
import packageJson from '../../../../package.json';

type Props = {
  open: boolean;
  setOpen: (bool: boolean) => void;
};
export const VersionModal: React.FC<Props> = ({ open, setOpen }: Props) => {
  // const [clientInfo, setClientInfo] = useState<
  //   | {
  //       version: string;
  //       assistantId: string;
  //       endPoint: string;
  //     }
  //   | 'none'
  // >('none');
  // useEffect(() => {
  //   window.ipcRenderer.invoke('get-version').then((val) => setClientInfo(val));
  // }, []);
  return (
    <Modal open={open} setOpen={setOpen} footer={false} width={400}>
      <div
        style={{
          textAlign: 'center',
          padding: '60px 0px 39px'
        }}
      >
        <img
          src={logo}
          style={{
            width: '100px',
            height: '100px'
          }}
        />
        <div
          style={{
            fontWeight: 'bold',
            fontSize: '18px',
            marginTop: '12px',
            color: '#121212'
          }}
        >
          牧言 智能客服
        </div>
        <div
          style={{
            fontSize: '14px',
            color: '#333333',
            marginTop: '32px'
          }}
        >
          Version {packageJson.version ? packageJson.version : '-'}
        </div>
      </div>
    </Modal>
  );
};

import { contextBridge, ipc<PERSON>enderer } from 'electron/renderer';

contextBridge.exposeInMainWorld('xBrowser', {
  on: (channel, listener) => {
    ipcRenderer.on(channel, listener);
    return () => {
      ipcRenderer.off(channel, listener);
    };
  },
  offAll: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },
  changeUrl: (url) => ipcRenderer.send('change-url', url),
  changeTab: (data) => ipcRenderer.send('change-tab', data)
});

const windowLoaded = new Promise((resolve) => {
  window.onload = resolve;
});

ipcRenderer.on('port', async (event) => {
  await windowLoaded;
  window.postMessage('port', '*', event.ports);
});

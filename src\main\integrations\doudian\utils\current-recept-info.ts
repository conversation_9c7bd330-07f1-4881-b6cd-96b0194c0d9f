import dayjs from 'dayjs';
import axios from 'axios';
// import * as Sentry from "@sentry/electron/main";

// 获取当前客服接待的信息
export const currentReceptInfo = async (page) => {
  return await page.evaluate(() => {
    (window as any).toggleLock(false);
    const customer = document.querySelector("div[data-guide='DATA_STATISTICS']");
    const newData: any = [];
    if (customer) {
      for (let i = 0; i < customer.children[0].children.length; i++) {
        const key = Object.keys(customer.children[0].children[i]).find(
          (item) => item.indexOf('__reactEventHandlers') !== -1
        );
        if (key) {
          newData.push(
            customer.children[0].children[i][key].children.map((item) =>
              JSON.parse(JSON.stringify(item.props.children.props?.item ?? {}))
            )
          );
        }
      }
    }
    (window as any).toggleLock(true);
    return newData.flat();
  });
};

/** 上报基础信息*/
export const uploadBaseInfo = async (doudian, ctx, shopInfo, agnetInfo) => {
  const userInfo = shopInfo.CustomerServiceInfo;

  try {
    const data = {
      created_at: dayjs().format('YYYY-MM-DD HH:mm:ss.SSSSSS'),
      customer_service_id: userInfo?.id ? String(userInfo?.id) : '',
      customer_service_name: userInfo?.screen_name ?? '',
      assistant_id: ctx.env.assistantId,
      tag: String(ctx.userManager?.startAt),
      meta_data: {
        doudian: doudian,
        agent: agnetInfo
      }
    };
    ctx.log.debug('上报抖店数据:', JSON.stringify(doudian), JSON.stringify(agnetInfo));
    await axios.post(`${ctx.env.endpoint}/workbench/customer_service_index`, data, {
      timeout: 5 * 1000
    });
  } catch (error) {
    ctx.log.debug('uploadBaseInfo上传失败', (error as Error).message);
    // Sentry.captureException(error);
  }
};

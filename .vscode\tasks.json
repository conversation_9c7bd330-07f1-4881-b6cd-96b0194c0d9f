{"version": "2.0.0", "tasks": [{"label": "start-muyan", "type": "shell", "command": "npm", "args": ["run", "start-muyan"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "new"}, "isBackground": true, "problemMatcher": {"pattern": {"regexp": "^(.*)$", "file": 1}, "background": {"activeOnStart": true, "beginsPattern": ".*", "endsPattern": ".*ready.*|.*started.*|.*listening.*"}}}]}
import React from 'react';
import styles from './index.module.css';
type Props = {
  children: React.ReactNode;
  className?: string;
  onClick?: (e) => void;
  style?: CSSModuleClasses;
  type?: 'primary' | 'default';
  disabled?: boolean;
};
const Button: React.FC<Props> = ({
  children,
  className,
  onClick,
  type = 'default',
  style,
  disabled = false
}) => {
  const styleMap = new Map().set('default', styles.default).set('primary', styles.primary);
  const disabledStyle = {
    opacity: 0.5
  };
  return (
    <div
      onClick={(e) => {
        if (disabled) return;
        onClick && onClick(e);
      }}
      style={{ ...style, ...(disabled ? disabledStyle : {}) }}
      className={`${styles.button} ${styleMap.get(type)} ${className}`}
    >
      {children}
    </div>
  );
};

export default Button;

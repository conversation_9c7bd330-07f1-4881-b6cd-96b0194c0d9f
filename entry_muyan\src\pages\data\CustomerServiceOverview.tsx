type ItemProps = {
  title: string;
  content: any;
};
const Item: React.FC<ItemProps> = ({ title, content }: ItemProps) => {
  return (
    <div
      style={{
        width: '152px',
        height: '100px',
        borderRadius: '4px',
        border: '1px solid #D6E0F4',
        boxSizing: 'border-box',
        padding: '18px 16px 17px',
        marginRight: '12px'
      }}
    >
      <div
        style={{
          fontSize: '14px',
          color: '#333333'
        }}
      >
        {title}
      </div>
      <div
        style={{
          fontWeight: '500',
          fontSize: '26px',
          color: '#333333',
          marginTop: '10px'
        }}
      >
        {content}
      </div>
    </div>
  );
};

type Props = {
  data: any;
};
const CustomerServiceOverview: React.FC<Props> = ({ data }: Props) => {
  return (
    <div
      style={{
        marginTop: '26px',
        display: 'flex',
        marginBottom: '10px'
      }}
    >
      <Item title="总接待数" content={data?.user_cnt ?? '-'} />
      <Item title="独立接待数" content={data.user_cnt - data.change_human_user_cnt || '-'} />
      <Item title="转人工数" content={data.change_human_user_cnt ?? '-'} />
      <Item
        title="独立接待率"
        content={
          (((data.user_cnt - data.change_human_user_cnt) / data.user_cnt) * 100
            ? (((data.user_cnt - data.change_human_user_cnt) / data.user_cnt) * 100).toFixed(2)
            : '-') + '%'
        }
      />
      {/* <Item title="下单数" content="" />
      <Item title="直接下单率" content="" />
      <Item title="转人工下单率" content="" /> */}
    </div>
  );
};

export default CustomerServiceOverview;

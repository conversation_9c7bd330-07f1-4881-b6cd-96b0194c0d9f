/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import style from './index.module.css';
import AiIcon from '@/assets/icons/ai_customer.svg';
import { observer } from 'mobx-react-lite';
import { Button, Tooltip } from 'antd';
type Props = {
  mainConfig: any;
  assistantsName: string;
};
const AiObservation: React.FC<Props> = observer(({ /*mainConfig,*/ assistantsName }) => {
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}
    >
      <div className={style.header}>
        <div>
          <div className={style.header_title}>
            <span className="name">{assistantsName}</span>
            <div className={style.header_btn}>
              <img src={AiIcon} />
              <span>AI客服</span>
            </div>
          </div>
          <Tooltip title={localStorage.getItem('assistantId')}>
            <div className={style.header_tip}>{localStorage.getItem('assistantId')}</div>
          </Tooltip>
        </div>
        {localStorage.getItem('roleCode') === 'admin' && (
          <Button
            type="primary"
            ghost
            onClick={() => {
              window.open(
                (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
                  '/#/?' +
                  encodeURIComponent(
                    // @ts-ignore get exist
                    `key=${localStorage.getItem('secret_key')}&token=${localStorage.getItem('accessToken')}&org=${localStorage.getItem('organization')}&user=${JSON.stringify(localStorage.getItem('user'))}`
                  ),
                '_blank',
                'noopener'
              );
            }}
          >
            AI训练
          </Button>
        )}
      </div>
    </div>
  );
});

export default AiObservation;

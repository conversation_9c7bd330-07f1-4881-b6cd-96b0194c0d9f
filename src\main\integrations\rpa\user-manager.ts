import User from './user';

class UserManager {
  #users: Record<string, User> = {};
  startAt: number = Date.now(); // 记录每次启动时间
  constructor(private ctx) {}

  get(user, shop?, welcome = true) {
    const key = shop ? `${user}_${shop}` : user;
    if (!this.#users[key]) {
      this.#users[key] = new User(this.ctx, user, shop);
      if (welcome) {
        this.#users[key].welcome();
        this.#users[key].preSale();
      }
    }

    return this.#users[key];
  }

  // TODO remove
}

export default UserManager;

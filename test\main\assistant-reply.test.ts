import { getImgPathByUrl } from '../../src/main/integrations/rpa/utils/download-image';
import path from 'node:path';
import { expect, test } from 'vitest';

test('picture in the middle', () => {
  const a = mergeContent([
    {
      type: 'text',
      text: [
        {
          value: '测试图片'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工2334'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '图片'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: '测试图片\n请转人工\n'
    },
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    },
    {
      type: 'txt',
      data: '\n请转人工\n图片'
    }
  ]);
});

test('picture in the middle2', () => {
  const a = mergeContent([
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '请转人工2334'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工2334'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: '请转人工\n'
    },
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    },
    {
      type: 'txt',
      data: '\n请转人工2334\n请转人工'
    }
  ]);
});

test('picture in the middle3', () => {
  const a = mergeContent([
    {
      type: 'text',
      text: [
        {
          value: '测试图片'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工2334'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: '测试图片\n请转人工\n'
    },
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    },
    {
      type: 'txt',
      data: '\n请转人工'
    }
  ]);
});

test('empty', () => {
  const a = mergeContent([]);
  expect(a.length).toEqual(0);
});

test('picture in the last', () => {
  const a = mergeContent([
    {
      type: 'text',
      text: [
        {
          value: '测试图片'
        }
      ]
    },
    {
      type: 'transfer',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: '测试图片\n请转人工\n'
    },
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    }
  ]);
});

test('picture in the first', () => {
  const a = mergeContent([
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '图片'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    },
    {
      type: 'txt',
      data: '\n请转人工\n图片'
    }
  ]);
});

test('picture only', () => {
  const a = mergeContent([
    {
      type: 'picture',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'image',
      data: {
        path: '/Users/<USER>/w/b/x-browser/test/main/images',
        file: '1.jpg'
      }
    }
  ]);
});

test('text only', () => {
  const a = mergeContent([
    {
      type: 'text',
      text: [
        {
          value: 'faffnjansdjkf'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: 'faffnjansdjkf'
    }
  ]);
});

test('text only2', () => {
  const a = mergeContent([
    {
      type: 'text',
      text: [
        {
          value: 'faffnjansdjkf'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: 'gef'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: 'faffnjansdjkf\ngef'
    }
  ]);
});

test('unknown type', () => {
  const a = mergeContent([
    {
      type: 'picture1',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '请转人工'
        }
      ]
    },
    {
      type: 'picture2',
      text: [
        {
          value:
            'https://img.alicdn.com/imgextra/i4/O1CN011m1p1i1w4n1n5n6n7/O1CN011m1p1i1w4n1n5n6n7.jpg'
        }
      ]
    },
    {
      type: 'text',
      text: [
        {
          value: '图片'
        }
      ]
    }
  ]);
  expect(a).toEqual([
    {
      type: 'txt',
      data: '请转人工\n图片'
    }
  ]);
});

function mergeContent(content: any[]) {
  const mergedContent: any[] = [];

  for (let j = 0; j < content.length; j++) {
    const cnt = content[j];
    if (cnt.type === 'picture') {
      const file = '/Users/<USER>/w/b/x-browser/test/main/images/1.jpg';
      const data = {
        type: 'image',
        data: {
          path: path.dirname(file),
          file: path.basename(file)
        }
      };
      mergedContent.push(data);
    } else {
      // 合并文本 text or transfer
      const data = {
        type: 'txt',
        data: j === 0 ? '' : '\n'
      };
      while (j < content.length && content[j].type !== 'picture') {
        if (content[j].type === 'text') {
          data.data += content[j].text[0].value + '\n';
        } else if (content[j].type === 'transfer') {
          data.data += '请转人工' + '\n';
        }
        j++;
      }
      j--;
      // data.data = data.data.trimEnd();
      mergedContent.push(data);
    }
  }
  if (mergedContent.length > 0 && mergedContent[mergedContent.length - 1].type === 'txt') {
    mergedContent[mergedContent.length - 1].data =
      mergedContent[mergedContent.length - 1].data.trimEnd();
  }
  return mergedContent;
}

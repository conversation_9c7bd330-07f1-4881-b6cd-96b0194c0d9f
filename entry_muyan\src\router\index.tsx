/* eslint-disable @typescript-eslint/no-explicit-any */
// import lazyLoad from "./utils/lazyLoad";
// import { lazy } from "react";
import { RouterProvider, createHashRouter, Navigate } from 'react-router-dom';
import App from '@/App';
import Home from '@/pages/home';
import AiObservation from '@/pages/ai_observation';
import AiSetup from '@/pages/ai_setup';
import Data from '@/pages/data';
import Settings from '@/pages/settings';
import Manual from '@/pages/ai_observation/manual';
import Login from '@/pages/login';
const rootRouter = [
  {
    path: '',
    element: <Navigate to="/login" />
  },
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '/home',
        element: <Home />
      },
      {
        path: '/aiobservation',
        element: <AiObservation />
      },
      {
        path: '/aisetup',
        element: <AiSetup />
      },
      {
        path: '/data',
        element: <Data />
      },
      {
        path: '/settings',
        element: <Settings />
      },
      {
        path: '/manual',
        element: <Manual />
      }
    ]
  },
  {
    path: '/login',
    element: <Login />
  }
];

const Router = (): React.ReactElement<any, string | React.JSXElementConstructor<any>> | null => {
  const router = createHashRouter(rootRouter);
  return <RouterProvider router={router} />;
};
export default Router;

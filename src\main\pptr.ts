import { app, WebContentsView } from 'electron';
import pie from 'puppeteer-in-electron';
import puppeteer from 'puppeteer-core';

class Pptr {
  #browser;
  #initialized = false;

  async initialize() {
    if (this.#initialized) {
      return;
    }
    await pie.initialize(app);
    this.#initialized = true;
  }

  async connect() {
    this.#browser = await pie.connect(app, puppeteer as any);
  }

  async getPage(view: WebContentsView): Promise<any> {
    if (!this.#browser) {
      throw new Error('puppeteer has not connected to app');
    }
    return pie.getPage(this.#browser, view as any);
  }
}

export const pptr = new Pptr();

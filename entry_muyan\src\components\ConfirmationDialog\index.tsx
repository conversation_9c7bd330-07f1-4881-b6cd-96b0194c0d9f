import Modal from '../Modal';
import { useEffect, useState } from 'react';
import { createRoot } from 'react-dom/client';
export function AlertDialog({
  open,
  title,
  description,
  onOK,
  onCancel
}: {
  open?: boolean;
  title: string;
  description?: string;
  onOK?: () => void;
  onCancel?: () => void;
  onOpenChange?: (open: boolean) => void;
}) {
  const [_open, setOpen] = useState(false);
  useEffect(() => {
    setOpen(open ?? false);
  }, [open]);
  return (
    <Modal
      title="提示"
      onOk={() => {
        if (onOK) onOK();
        setOpen(false);
      }}
      onCancel={() => {
        if (onCancel) onCancel();
        setOpen(false);
      }}
      open={_open}
      setOpen={setOpen}
    >
      <div
        style={{
          textAlign: 'center',
          margin: '20px'
        }}
      >
        <div>{title}</div>
        <div>{description}</div>
      </div>
    </Modal>
  );
}

export const ConfirmationDialog = ({ title, desc }: { title: string; desc?: string }) => {
  const root = createRoot(document.getElementById('confirmation-dialog-root')!); // createRoot(container!) if you use TypeScript
  return new Promise((resolve, reject) => {
    const handleConfirm = () => {
      resolve(true);
      closeDialog();
    };
    const handleCancel = () => {
      reject();
      closeDialog();
    };

    const closeDialog = () => {
      root.unmount();
    };

    root.render(
      <AlertDialog
        open={true}
        onOK={handleConfirm}
        onCancel={handleCancel}
        title={title}
        description={desc}
      />
    );
  });
};

export const ConfirmationDialogProvider = () => {
  return <div id="confirmation-dialog-root" />;
};

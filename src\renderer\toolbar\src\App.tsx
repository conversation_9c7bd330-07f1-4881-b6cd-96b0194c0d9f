import { useEffect, useRef, useState } from 'react';
import Tabs, { TabView } from './components/Tabs';
import Address from './components/Address';

const useListenerRef = (fn) => {
  const listenerRef = useRef(fn);
  listenerRef.current = fn;
  return listenerRef;
};

function App(): JSX.Element {
  const [url, setUrl] = useState('');
  const [tabs, setTabs] = useState([] as TabView[]);
  const [activeTab, setActiveTab] = useState(-1);
  const [[canGoBack, canGoForward], setNavigationHistoryHistory] = useState([false, false]);
  const [hideAddress, setHideAddress] = useState(true);
  const [hideFavicon, setHideFavicon] = useState(true);

  const onChangeTab = useListenerRef((data): void => {
    const { action, payload } = data;
    console.log('onChangeTab', action, payload);

    switch (action) {
      case 'new': {
        const { id, url, title, isLoading, closeable } = payload;
        setTabs((prev) => {
          return [
            ...prev,
            {
              id,
              url,
              title,
              isLoading,
              closeable
            }
          ];
        });
        break;
      }
      case 'active': {
        const { id, url, canGoBack, canGoForward } = payload;
        setActiveTab(id);
        setUrl(url);
        setNavigationHistoryHistory([canGoBack, canGoForward]);
        break;
      }
      case 'update': {
        const { id, url, title, canGoBack, canGoForward, faviconUrl, isLoading } = payload;
        setTabs((tabs) =>
          tabs.map((tab) => {
            if (tab.id === id) {
              return { id, url, title, faviconUrl, isLoading, closeable: tab.closeable };
            }
            return tab;
          })
        );

        if (id === activeTab || activeTab === -1) {
          setUrl(url);
          setNavigationHistoryHistory([canGoBack, canGoForward]);
        }
        break;
      }
      case 'delete': {
        const { id } = payload;
        setTabs((tab) => {
          return tab.filter((t) => t.id !== id);
        });
        break;
      }
    }
  });

  useEffect(() => {
    const offChangeTab = window.xBrowser.on('change-tab', (_, data) => {
      onChangeTab.current(data);
    });
    const offHideAddressBar = window.xBrowser.on('hide-address-bar', (_, hide) => {
      setHideAddress(hide);
    });
    const offHideFavicon = window.xBrowser.on('hide-favicon', (_, hide) => {
      setHideFavicon(hide);
    });

    return () => {
      offChangeTab();
      offHideAddressBar();
      offHideFavicon();
    };
  }, []);

  return (
    <>
      <Tabs
        active={activeTab}
        tabs={tabs}
        changeTab={window.xBrowser.changeTab}
        hideAdd={hideAddress}
        hideFavicon={hideFavicon}
        hideAddress={hideAddress}
      />
      {!hideAddress && (
        <Address
          url={url}
          onUrlChange={setUrl}
          canGoBack={canGoBack}
          canGoForward={canGoForward}
          goBack={window.xBrowser.goBack}
          goForward={window.xBrowser.goForward}
          reload={window.xBrowser.reload}
        />
      )}
    </>
  );
}

export default App;

import { app, WebContentsView } from 'electron';
import pie from 'puppeteer-in-electron';
import puppeteer from 'puppeteer-core';

class Pptr {
  #browser;

  async initialize() {
    await pie.initialize(app);
  }

  async connect() {
    this.#browser = await pie.connect(app, puppeteer as any);
  }

  async getPage(view: WebContentsView): Promise<any> {
    if (!this.#browser) {
      throw new Error('puppeteer has not connected to app');
    }
    return pie.getPage(this.#browser, view as any);
  }
}

export const pptr = new Pptr();

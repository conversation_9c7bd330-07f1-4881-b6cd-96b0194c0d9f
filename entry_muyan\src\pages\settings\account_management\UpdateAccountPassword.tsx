/* eslint-disable @typescript-eslint/no-explicit-any */
import Modal from '@/components/Modal';
import { Form, Input, message } from 'antd';
// import style from "./index.module.css";
import { setPassword } from '@/api/myqaApi';
type Props = {
  open: boolean;
  userId: string;
  userName: string;
  setOpen: (bool: boolean) => void;
  onSubmit: () => void;
};
const UpdateAccount: React.FC<Props> = ({ open, setOpen, userId, userName, onSubmit }: Props) => {
  const [form] = Form.useForm();
  const onFinish = (values): void => {
    setPassword({
      user_id: userId,
      password: values.password
    }).then(() => {
      resetFields();
      onSubmit();
      message.success('修改成功');
      let accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
      accountData = accountData.filter((item) => {
        return item.username !== userName;
      });
      localStorage.setItem(
        'accountData',
        JSON.stringify([
          ...accountData.filter((item) => {
            return item.username !== userName;
          })
        ])
      );
    });
  };

  const resetFields = () => {
    setOpen(false);
    form.resetFields();
  };
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      width={480}
      title="修改密码"
      okText="保存"
      onOk={() => form.submit()}
      onCancel={() => resetFields()}
    >
      <div
        style={{
          width: '432px',
          margin: '24px auto'
        }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 21 }} form={form} onFinish={onFinish}>
          <Form.Item
            name="password"
            label="密码："
            rules={[
              {
                required: true,
                pattern: /^[A-Za-z0-9]{6,64}$/,
                message: '请输入6-64位数字字母组合密码'
              }
            ]}
          >
            <Input.Password placeholder="请输入密码" />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default UpdateAccount;

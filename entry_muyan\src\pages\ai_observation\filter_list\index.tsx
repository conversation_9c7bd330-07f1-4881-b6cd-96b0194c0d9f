/* eslint-disable react/prop-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import style from './index.module.css';
import { Spin, Tabs, Empty } from 'antd';
import { useEffect, useState } from 'react';
import Item from './list_item';
import TransferItem from './list_item/TransferItem';
import SearchThread from './SearchThread';
import Header from './Header';
import { applicationTransfer } from '@/api/myqaApi';
import { ExtractSession } from '../ExtractSession';
import type { TabsProps } from 'antd';
import default_content from '@/assets/imgs/default_content.png';

type Props = {
  activeId: string;
  clearInfo: () => void;
  assistantsName: string;
  messageData: any[];
  onChange: (id: string, status: boolean) => void;
  setTransfer: (val) => void;
  setBeSilent: (bool: boolean) => void;
  isManual?: boolean; // 是否是转人工
  setStartTime: (val) => void;
};
const items: TabsProps['items'] = [
  {
    key: '1',
    label: '静默采集'
  },
  // {
  //   key: "2",
  //   label: "转人工",
  // },
  {
    key: '3',
    label: '正式开线'
  }
];
// const url = await window.ipcRenderer.invoke('get-version');
const AiObservation: React.FC<Props> = ({
  activeId,
  clearInfo,
  onChange,
  assistantsName,
  messageData,
  setTransfer,
  setBeSilent,
  isManual,
  setStartTime
}) => {
  const [value, serValue] = useState<string>('');
  const [fliterData, setFileList] = useState<any[]>([]);
  const [data, setData] = useState<any[]>([]);
  const [viewKey, setViewKey] = useState<string>('1');
  const [mainConfig /*setMainConfig*/] = useState<any[]>([]);
  const [transferList, setTransferList] = useState<any[]>([]);
  const [timer, setTimer] = useState<NodeJS.Timeout | null>(null);
  const [open, setOpen] = useState(false);
  const [sub, setSub] = useState(0);
  useEffect(() => {
    // window.ipcRenderer.invoke('get-main-app').then((data) => {
    //   setMainConfig(data[0]);
    // });
    return () => {
      if (timer) {
        clearInterval(timer);
        setTimer(null); // 清除状态，避免内存泄漏
      }
    };
  }, []);
  const [loading, setLoading] = useState<boolean>(false);

  const getTransferData = (isloading = true) => {
    if (isloading) setLoading(true);
    applicationTransfer()
      .then((res) => {
        setTransferList(res.data.data);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getCurrentThread = (activeId: string) => {
    return data.find((item) => item.id === activeId) ?? {};
  };
  const onTabChange = (key: string) => {
    clearInfo();
    setViewKey(key);
    setData([]);
    if (key === '1') {
      setTransfer({});
      setBeSilent(false);
      if (timer) {
        clearInterval(timer);
        setTimer(null); // 清除状态，避免内存泄漏
      }
    }
    if (key === '2') {
      getTransferData();
      setBeSilent(false);
      setTimer(
        setInterval(() => {
          getTransferData(false);
        }, 10000)
      );
    }
    if (key === '3') {
      setTransfer({});
      setBeSilent(true);
      if (timer) {
        clearInterval(timer);
        setTimer(null); // 清除状态，避免内存泄漏
      }
    }
  };
  // useEffect(() => {
  //   if (isManual) {
  //     onTabChange("2");
  //   }
  // }, [isManual]);
  return (
    <div className={`${style.filter_list} filter`}>
      {!isManual && (
        <div>
          <Header assistantsName={assistantsName} mainConfig={mainConfig} />
          <div className={style.title}>对话列表</div>
          <Tabs defaultActiveKey="1" items={items} onChange={onTabChange} />
          <SearchThread
            value={value}
            serValue={serValue}
            viewKey={viewKey}
            setData={(data, newArr, search) => {
              const fliterList = newArr?.length > 0 ? newArr : fliterData;
              clearInfo();
              setData(data);

              if (viewKey !== '2') {
                const itemData = data.filter((item) => {
                  if (fliterList.length === 0 && !search) return true;
                  return fliterList.find((f) => f.thread_id === item.id);
                })[0];

                if (itemData)
                  onChange(itemData.id, itemData?.origin?.source === 'manual' ? true : false);
              } else {
                const id = transferList.filter((item) => item?.status === 'failed')[0]
                  ?.conversation_id;
                if (id) onChange(id, false);
              }
            }}
            setLoading={setLoading}
            setFileList={setFileList}
            onChange={(e) => onChange(e, false)}
            sub={sub}
            setStartTime={setStartTime}
          />
        </div>
      )}
      <div className={style.total}>
        共
        {['1', '3'].includes(viewKey)
          ? data.filter((item) => {
              if (fliterData.length === 0 && !value) return true;
              return fliterData.find((f) => f.thread_id === item.id);
            }).length
          : transferList.length}
        条
      </div>
      <Spin tip="Loading..." spinning={loading}>
        <div className={`${style.content} ${['2'].includes(viewKey) ? style.contenttwo : ''}`}>
          {['1', '3'].includes(viewKey) &&
            (data.filter((item) => {
              if (fliterData.length === 0 && !value) return true;
              return fliterData.find((f) => f.thread_id === item.id);
            })?.length > 0 ? (
              data
                .filter((item) => {
                  if (fliterData.length === 0 && !value) return true;
                  return fliterData.find((f) => f.thread_id === item.id);
                })
                .map((item) => {
                  return (
                    <div
                      key={item.id}
                      onClick={() =>
                        onChange(item.id, item?.origin?.source === 'manual' ? true : false)
                      }
                    >
                      <Item
                        data={item}
                        active={item.id === activeId}
                        extract={() => setOpen(true)}
                      />
                    </div>
                  );
                })
            ) : (
              <div style={{ marginTop: 'calc(50% - 100px)' }}>
                <Empty
                  image={default_content}
                  imageStyle={{
                    height: 120
                  }}
                  description={
                    <span
                      style={{
                        fontSize: '12px',
                        color: '#666666',
                        lineHeight: '18px'
                      }}
                    >
                      暂无内容
                    </span>
                  }
                />
              </div>
            ))}
          {viewKey === '2' &&
            (transferList.filter((item) => item?.status === 'failed')?.length > 0 ? (
              transferList
                .filter((item) => item?.status === 'failed')
                .map((item) => {
                  return (
                    <div
                      key={item.conversation_id}
                      onClick={() => {
                        setTransfer(item);
                        onChange(item.conversation_id, false);
                      }}
                    >
                      <TransferItem
                        data={item}
                        // url={url.endPointV2 ?? ''}
                        url={''}
                        name={assistantsName}
                        active={item.conversation_id === activeId}
                      />
                    </div>
                  );
                })
            ) : (
              <div style={{ marginTop: 'calc(50% - 40px)' }}>
                <Empty
                  image={default_content}
                  imageStyle={{
                    height: 120
                  }}
                  description={
                    <span
                      style={{
                        fontSize: '12px',
                        color: '#666666',
                        lineHeight: '18px'
                      }}
                    >
                      暂无内容
                    </span>
                  }
                />
              </div>
            ))}
        </div>
      </Spin>
      <ExtractSession
        currentThread={getCurrentThread(activeId)}
        open={open}
        onOpenChange={setOpen}
        messageData={messageData}
        onSubmit={() => {
          setSub(sub + 1);
        }}
      />
    </div>
  );
};

export default AiObservation;

/* eslint-disable @typescript-eslint/no-explicit-any */
import image_icon from '@/assets/imgs/image_icon.png';
import style from './index.module.css';
import icon_add from '@/assets/icons/icon_add.svg';
import Button from '@/components/Button';
import React, { useState } from 'react';
import PlatformSelection from '../PlatformSelection';
type Props = {
  getData: (val?: string) => void;
};
const Default: React.FC<Props> = ({ getData }) => {
  const [open, setOpen] = useState(false);
  return (
    <div className={style.default}>
      <img className={style.img} src={image_icon} alt="img_icon" />
      <div className={style.tip}>暂无托管账户 请添加</div>
      <br />
      <Button className={style.button} type="primary" onClick={() => setOpen(true)}>
        <img
          style={{
            width: '12px',
            height: '12px',
            marginRight: '5px',
            position: 'relative',
            top: '1px'
          }}
          src={icon_add}
          alt="icon_add"
        />
        <span>添加托管账户</span>
      </Button>
      <PlatformSelection data={[]} getData={getData} open={open} setOpen={setOpen} />
    </div>
  );
};

export default Default;

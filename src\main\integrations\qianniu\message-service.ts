import { db } from '../..//utils/sqlite';
import dayjs from 'dayjs';
import { createPromiseCapability } from '../../utils/promise';

class MessageService {
  #ctx: {
    log?: any;
    emit?: any;
    env?: any;
  };

  #openTime: string;
  #activeUid: string;
  #replyList: ReturnType<typeof createPromiseCapability>;

  constructor(ctx) {
    this.#ctx = ctx;
    this.#openTime = dayjs().format();
  }

  /**
   id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
   open_time TEXT NOT NULL,
   shop TEXT NOT NULL,
   uid TEXT NOT NULL,
   nickname TEXT NOT NULL,
   answer TEXT NOT NULL,
   question TEXT NOT NULL,
   create_time TEXT NOT NULL,
   updated_time TEXT DEFAULT NULL,
   */
  addReply(
    uuid,
    shop,
    uid,
    nickname,
    answer,
    question: { question: string; time: string }[] = [],
    cb?: (data: any) => void
  ) {
    const stmt = db.prepare(
      'INSERT INTO message (uuid, open_time, shop, uid, nickname, answer, question, create_time, updated_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)'
    );
    stmt.run(
      uuid,
      this.#openTime,
      shop,
      uid,
      nickname,
      answer,
      JSON.stringify(question),
      dayjs().format(),
      dayjs().format()
    );

    if (this.#activeUid === uid) {
      cb?.({
        uuid,
        shop,
        uid,
        nickname,
        answer,
        question: JSON.stringify(question)
      });
    }
  }

  async getReplyList(shop, uid) {
    // if (this.#activeUid === uid) {
    //   return this.#replyList.promise;
    // }
    this.#activeUid = uid;
    this.#replyList = createPromiseCapability();
    db.all(
      'SELECT * FROM message WHERE open_time = ? AND shop = ? AND uid = ? ORDER BY id DESC',
      this.#openTime,
      shop,
      uid,
      (err, rows) => {
        if (err) {
          this.#ctx.log.error('get reply list error', err);
          this.#replyList.reject(err);
        }

        this.#replyList.resolve(rows);
      }
    );

    return this.#replyList.promise;
  }
}

export default MessageService;

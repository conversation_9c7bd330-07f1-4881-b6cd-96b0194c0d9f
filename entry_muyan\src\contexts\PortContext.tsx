import { createContext, useContext, useMemo } from 'react';
import useMessagePort from '@/hooks/useMessagePort';

const PortContext = createContext<{
  ports: any;
  postMessage: (data: any, target?: string) => void;
  onmessage: (callback: (data: any) => void) => void;
}>({
  ports: {},
  postMessage: () => {},
  onmessage: () => {}
});

export const PortProvider = ({ children }: { children: React.ReactNode }) => {
  const { postMessage, onmessage, ports } = useMessagePort();
  const value = useMemo(() => ({ postMessage, onmessage, ports }), [postMessage, onmessage, ports]);

  return <PortContext.Provider value={value}>{children}</PortContext.Provider>;
};

export const usePort = () => useContext(PortContext);

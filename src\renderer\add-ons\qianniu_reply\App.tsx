/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import _ from 'lodash';
import Help from './help';
import './toast.css';
import { <PERSON><PERSON>, Popconfirm } from 'antd';
import running from './img/running.svg';
import waitReply from './img/wait-reply.svg';
import disconnect from './img/disconnect.svg';
import error from './img/error.svg';
import confirm from './img/confirm.svg';
// import button from '../../../../entry_muyan/src/components/Button';

// window.onbeforeunload = (event) => {
//   // Recommended
//   event.preventDefault();
//
//   // Included for legacy support, e.g. Chrome/Edge < 119
//   event.returnValue = true;
// };

type ToastMessage = {
  sender?: number;
  to?: number;
  topic: string;
  payload: {
    uid: string;
    cs: string;
    shop: string;
    nickname: string;
    time: number;
    message: string;
  };
};

// function usePrevious(value) {
//   const ref = useRef();
//   useEffect(() => {
//     ref.current = value;
//   });
//   return ref.current;
// }

const waitLimit = 3 * 60 * 1000;
const renderTime = (time, now, close) => {
  const estimate = dayjs(time).add(waitLimit);
  const t = dayjs(time);
  const n = dayjs(now);
  if (estimate.isAfter(n)) {
    return <span className="countdown red">{estimate.diff(n, 's')}秒后超时</span>;
  } else {
    return (
      <span className="countdown orange">
        {`已等待${n.diff(t, 'm')}分钟`}
        <span className="close" onClick={close}>
          x
        </span>
      </span>
    );
  }
};

// const handleItemClick = (message) => {
//   return (_e) => {
//     window.electronAPI.callbackToastMessage({
//       to: message.sender,
//       topic: 'response_to_user',
//       payload: message.payload
//     });
//   };
// };

const App: React.FC = () => {
  const [unDealMessage, setUnDealMessage] = useState<any>({});
  const [currentTime, setCurrentTime] = useState(dayjs().valueOf());
  const [clientStatus, setClientStatus] = useState<string>('启动中');
  const [exeStatus, setExeStatus] = useState<string>('未启动');
  const [replyMsg, setReplyMsg] = useState<string>('');
  const [runningStatus, setRunningStatus] = useState<
    'connecting' | 'connect' | 'wait-reply' | 'disconnect' | 'error'
  >('connecting');
  const port = useRef<any>();
  const statusMemory = useRef<string>('connect');
  const [timer, setTimer] = useState<number>(0);
  const timerId = useRef<any>();

  useEffect(() => {
    // window.addEventListener('message', (e) => {
    //   const { source, data } = e;
    //
    //   if (source === window) {
    //     return;
    //   }
    //
    //   port.current = source;
    //   console.log('received', e);
    //
    //   if (typeof data === 'object') {
    //     if (data.type === 'status') {
    //       setRunningStatus(data.status);
    //     }
    //   }
    // });
    window.xBrowser.on('qianniu-client-status', (_, status) => {
      console.log('qianniu-client-status', status);
      setRunningStatus((old) => {
        // if (old === 'connecting' && status === 'error') {
        //   return old;
        // }
        return status;
      });
    });
    window.xBrowser.on('qianniu-wait-reply', (_) => {
      console.log('receive qianniu-wait-reply', +new Date());
      // statusMemory.current = 'wait-reply';
      setRunningStatus('wait-reply');
      // console.log('qianniu-client-status', status)
      // setClientStatus((old) => {
      //   switch (status) {
      //     case 'connect':
      //       return '已启动';
      //     case 'error':
      //       if (old === '启动中') {
      //         return old;
      //       }
      //       return '错误';
      //     case 'disconnect':
      //       return '已停止';
      //     default:
      //       return '未知';
      //   }
      // });
      // if (replyWindow.current) {
      //   replyWindow.current.postMessage(
      //     {
      //       type: 'status',
      //       status
      //     },
      //     '*'
      //   );
      // }
    });

    // const timerId = setInterval(() => {
    //   setTimer(timer => Math.min(timer + 1, 999));
    // }, 1000);
    //
    // return () => {
    //   clearInterval(timerId);
    // };
  }, []);

  useEffect(() => {
    if (runningStatus === 'wait-reply') {
      if (!timerId.current) {
        timerId.current = setInterval(() => {
          setTimer((timer) => Math.min(timer + 1, 999));
        }, 1000);
      }
    } else if (runningStatus === 'disconnect') {
      // no op
    } else {
      if (timerId) {
        clearInterval(timerId.current);
        timerId.current = null;
        setTimer(0);
      }
    }

    // let timerId: any;
    // if (runningStatus === 'wait-reply') {
    //   timerId = setInterval(() => {
    //     setTimer((timer) => Math.min(timer + 1, 999));
    //   }, 1000);
    // } else {
    //   setTimer(0);
    // }
    //
    // return () => {
    //   if (timerId) clearInterval(timerId);
    // };
  }, [runningStatus]);

  if (runningStatus === 'wait-reply') {
    console.log('wait-reply', +new Date());
  }

  switch (runningStatus) {
    case 'connecting':
    case 'connect':
      return (
        <div className="status-wrap">
          <div className="top">
            <div className="icon">
              <img src={running} alt="" />
            </div>
            <div className="right">
              <h3>消息采集中，请勿移动鼠标！</h3>
              {/*<div className="content">消息采集过程中请勿移动鼠标！</div>*/}
            </div>
          </div>
        </div>
      );
    case 'wait-reply':
      return (
        <div className="status-wrap wait-reply">
          <div className="top">
            <div className="icon">
              {/*<img src={waitReply} alt="" />*/}
              <div className="timer">{timer}s</div>
            </div>
            <div className="right">
              <h3 className="confirm">请确认输入框中回复，确认完成后点击完成发送</h3>
              {/*<div className="content">请确认输入框中AI回复，确认完成后点击发送</div>*/}
            </div>
            {/*<div className="timer">{timer}s</div>*/}
          </div>
          <div className="footer">
            <Popconfirm
              title="确认已转交人工？"
              // description="Are you sure to delete this task?"
              icon={<img src={confirm} />}
              onConfirm={() => {
                window.xBrowser.send('transferred');
                setRunningStatus('connect');
                statusMemory.current = 'connect';
              }}
              onCancel={() => {}}
              okText="确认"
              okButtonProps={{ autoInsertSpace: false }}
              cancelText="取消"
              cancelButtonProps={{ autoInsertSpace: false }}
              arrow={false}
              placement="left"
            >
              <Button>已转交</Button>
            </Popconfirm>
            <Button
              type="primary"
              onClick={() => {
                window.xBrowser.send('handover');
                // setRunningStatus('connect');
                // statusMemory.current = 'connect';
                // port.current.postMessage({ type: 'handover' }, '*');
                // setRunningStatus('connect');
              }}
            >
              完成发送
            </Button>
          </div>
        </div>
      );
    case 'disconnect':
      return (
        <div className="status-wrap">
          <div className="top">
            <div className="icon">
              <img src={disconnect} alt="" />
            </div>
            <div className="right">
              <h3>
                辅助服务已经停用
                <br />
                使用Ctrl+Shift+Q再次启用
              </h3>
              {/*<div className="content">AI辅助服务已经停用，使用Command/Ctrl +Shift+Q再次启用</div>*/}
            </div>
          </div>
        </div>
      );
    case 'error':
      return (
        <div className="status-wrap">
          <div className="top">
            <div className="icon">
              <img src={error} alt="" />
            </div>
            <div className="right">
              <h3>程序运行出现异常，请重启或联系牧言工作人员</h3>
              {/*<div className="content">AI辅助服务已经停用，使用Command/Ctrl +Shift+Q再次启用</div>*/}
            </div>
          </div>
        </div>
      );
  }
};

export default App;

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useRef, useState } from 'react';
import FilterList from './filter_list';
import Chat from './chat';
import Log from './log';
import { getMessages, getMessagesMetadata, getAssistants } from '@/api/myqaApi';
import { store } from '@/store';
import cloneDeep from 'lodash-es/cloneDeep';
import dayjs from 'dayjs';
const AiObservation: React.FC = () => {
  const [showLog, setShowLog] = useState(false);
  const [activeId, setActiveId] = useState('');
  const [messageData, setMessageData] = useState<any>([]);
  const [metadata, setMetadata] = useState<any>({});
  const [logViewKey, setLogViewKey] = useState('1');
  const [loading, setLoading] = useState(false);
  const chatRef = useRef<any>(null);
  const intervalRef = useRef<any>(null);
  const [startTime, setStartTime] = useState<any>(dayjs(new Date()).unix()); //时间范围开始时间
  const { message } = store;
  const getMessagesList = (id: string, isloading = true): any => {
    if (loading) return;
    if (isloading) {
      setLoading(true);
      setMessageData([]);
    }
    setActiveId(id);
    return getMessages(id)
      .then((res) => {
        const data = cloneDeep(res.data).reverse();
        setMessageData(data);
        message.setQueryIur(
          data
            .filter((item) => {
              return item?.role !== 'user';
            })
            .map((item) => {
              return {
                id: item.id,
                iur: item?.metadata?.answer_meta?.iur ?? '',
                recall: item?.metadata?.answer_meta?.msg ?? 'NO_DOCS_FOUND'
              };
            })
        );
        if (chatRef.current && isloading) {
          setTimeout(() => {
            chatRef.current.scrollToBottom();
          }, 10);
        }
        return res;
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // const poll = (id) => {
  //   intervalRef.current = setInterval(() => {
  //     getMessagesList(id, false);
  //   }, 10000);
  // };

  const getLogData = (id: string): void => {
    getMessagesMetadata({ thread_id: activeId, message_id: id }).then((res: any) => {
      setMetadata(res.metadata);
    });
  };
  const [name, setName] = useState('');
  const [currentAssistant, setCurrentAssistant] = useState<any>({});
  const [transfer, setTransfer] = useState<any>({});
  const [beSilent, setBeSilent] = useState<any>(false);
  useEffect(() => {
    getAssistants().then((res) => {
      const data = res.data?.find((item) => item.id === localStorage.getItem('assistantId'));
      setCurrentAssistant(data ?? {});
      setName(data?.name ?? '小可');
    });
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'start',
        justifyContent: 'space-between',
        height: '100%'
      }}
    >
      <FilterList
        assistantsName={name}
        activeId={activeId}
        onChange={(id: string, status: boolean) => {
          getMessagesList(id);
          setBeSilent(status);
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
          }
          // poll(id);
        }}
        clearInfo={() => {
          setMessageData([]);
          setActiveId('');
        }}
        setBeSilent={setBeSilent}
        setTransfer={setTransfer}
        messageData={messageData}
        isManual={true}
        setStartTime={setStartTime}
      />
      <Chat
        ref={chatRef}
        loading={loading}
        data={messageData}
        beSilent={beSilent}
        name={name}
        transfer={transfer}
        onChange={(id: string) => getMessagesList(id, false)}
        setShowLog={(val, id) => {
          getLogData(id);
          setShowLog(val);
          setLogViewKey('1');
        }}
        isManual={true}
        startTime={startTime}
      />
      <Log
        showLog={showLog}
        currentAssistant={currentAssistant}
        logViewKey={logViewKey}
        setLogViewKey={setLogViewKey}
        metadata={metadata}
        onClose={setShowLog}
      />
    </div>
  );
};

export default AiObservation;

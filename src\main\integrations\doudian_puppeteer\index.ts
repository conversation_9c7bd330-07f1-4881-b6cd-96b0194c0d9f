// import AutoClient from '../auto-client';
import UserManager from '../rpa/user-manager';
// import { Logger } from '../../log';
// import { createPromiseCapability, PromiseCapability } from '../promise-capability';
import { createPromiseCapability } from '../../utils/promise';
import PQueue from 'p-queue';
import { msgCreate } from './utils/msg-create';
import { toggleLock } from './utils/toggle-lock';
import { sendMessage } from './utils/send-message';
import { updateAgentStatue } from './utils/update-agent-statue';
import { transferUser } from './utils/transfer-user';
import { getSessionMessage } from './utils/get-session-message';
import { getCurrentUesrMessage, getCurrentUserMessageApi } from './utils/get-current-uesr-message';
import { statusSwitchBtn } from './utils/status-switch-btn';
import { loadConfig } from '../rpa/agent-config';
import { sendImage } from './utils/send-image';
import { Page } from 'puppeteer-core';
import ms from 'ms';
import { TimerActivate } from './utils/press-for-payment';
// import { DingtalkBot } from 'dd-bot';
import { setupReminders } from './utils/setup-remindersr';
import { currentReceptInfo, uploadBaseInfo } from './utils/current-recept-info';
import { SystemStatusManager } from './utils/agent-info';
import { uploadGoodsInfo, formatGoodsList } from './utils/shop-Info';
import ExpressTracker from '../rpa/kuaidi100';
import _ from 'lodash';
import { needTransfer } from '../rpa/utils/need-transfer';
import { Integration } from '../Integration';
import { WebContentsView } from 'electron';
import { Logger } from 'log4js';

// import { IntegrationConfig } from '../index';
// import { transpile } from 'typescript';
class Doudian extends Integration {
  // @ts-ignore maybe use later
  #config: PddConfig | undefined;
  #page: Page | undefined;
  #runningPromise: ReturnType<typeof createPromiseCapability>;
  #ctx: {
    env?: any;
    emit?: any;
    userManager?: UserManager;
    log?: Logger;
    agentConfig?: any;
  };
  #myqaSentMessage: string[];
  #waitingToBeSent: null | NodeJS.Timeout;
  #isSilence: boolean;
  #agentStatue: string;
  // #dingtalkBot: DingtalkBot;
  #userName: string;
  #shopInfo: any;
  #goodsList: any[];
  #msgQueue = new PQueue({
    concurrency: 1,
    timeout: ms('1m'),
    throwOnTimeout: false
  });
  #agentConfig: {
    transer_chat: string;
    transfer_blacklist: string[];
    press_interval: number[];
    press_payment_list: string[];
    pressImgs: string[];
    ding_token: string;
    greeting: string;
    kuaidi: {
      customer: string;
      key: string;
      template_name: string;
      queryUrl: string;
    };
  };
  #durations: number[];
  #systemStatusManager: SystemStatusManager;
  #userList: {
    [key: string]: {
      user_id: string;
      timerActivate?: TimerActivate;
      orderInfo: any[];
    };
  };
  #expressTracker: ExpressTracker;
  #currentReceptInfoInterval: NodeJS.Timeout;
  #webContentsView: WebContentsView;

  constructor(tab, config) {
    super(tab, config);
    this.#config = config;
    this.#ctx = {};
    this.#ctx.env = {
      endpoint: config.options.endpoint,
      assistantId: config.options.assistantId,
      secretKey: config.options.secretKey,
      isSilence: config.options.isSilence
    };
    this.#ctx.log = this.getLogger();
    this.#ctx.emit = this.emit.bind(this);
    this.#ctx.userManager = new UserManager(this.#ctx);
    this.#runningPromise = createPromiseCapability();
    this.#myqaSentMessage = [];
    this.#isSilence = config.options.isSilence;
    this.#waitingToBeSent = null;
    this.#agentStatue = 'pending';
    this.#userList = {};
    this.#durations = [];
    this.#systemStatusManager = new SystemStatusManager(
      this.#config?.version,
      this.#ctx.userManager.startAt
    );
    this.#webContentsView = tab.view;
  }

  async userMsgCreate(postData) {
    const self = {
      myqaSentMessage: this.#myqaSentMessage,
      page: this.#page,
      waitingToBeSent: this.#waitingToBeSent,
      ctx: this.#ctx,
      agentConfig: this.#agentConfig
    };

    msgCreate(
      self,
      postData,
      this.#isSilence,
      (arr) => this.setMyqaSentMessage(arr),
      () => this.#systemStatusManager.addTotalReceiveMsgCount()
    );
  }
  async setMyqaSentMessage(arr) {
    this.#myqaSentMessage = [...this.#myqaSentMessage, ...arr];
  }

  // 加载阿根廷配置文件
  async setAgentConfig() {
    const conf: any = await loadConfig(
      this.#ctx.env.endpoint,
      this.#ctx.env.assistantId,
      this.#ctx.log
    );
    if (!conf?.meta_data) {
      const e = new Error();
      e.name = '配置文件加载失败';
      throw e;
    }
    this.#agentConfig = conf.meta_data;
    this.#ctx.agentConfig = conf.meta_data;
    this.#ctx.log?.debug('配置文件加载完成');
    this.#expressTracker = new ExpressTracker(this.#agentConfig.kuaidi, this.#ctx.log);
  }

  async sendMessage(uid, content, type, sendType: string = 'dialogue') {
    if (!this.#isSilence) {
      this.#msgQueue.add(async () => {
        // 上锁防止新消息进线切换会话
        await this.#page!.evaluate(() => {
          (window as any).toggleLock(false);
        });

        if (sendType === 'rush') {
          if (this.#userList[uid]?.orderInfo?.length > 0) {
            this.#ctx.log?.debug(uid, '有订单不催付');
            return;
          }
        }
        this.#systemStatusManager.addMyqaAnswered();
        // 发送消息
        await this.#page!.evaluate(
          ({ id, msg, agentConfig, type }) => {
            (window as any).setTextAreaAndSend(id, msg, agentConfig, type);
          },
          {
            id: uid,
            msg: content,
            agentConfig: this.#agentConfig,
            type
          }
        );
        // 开锁
        await this.#page!.evaluate(() => {
          (window as any).toggleLock(true);
        });
      });
    }
  }

  async start() {
    this.#ctx.log?.debug('doudian-start');
    this.run();
  }

  async stop() {
    this.#ctx.log?.debug('doudian-close');
    this.sendMessageToOpener('exit', {
      id: this.#config.id
    });
    this.doStop();
  }

  async run() {
    // 抖店 为 0 小休(忙碌) 1 在线 2离线 => 号服保状态 0 小休(忙碌) 1 在线 3 离线
    const hfbStatusMap = new Map().set(0, 0).set(1, 1).set(2, 3);
    try {
      this.#page = await this.getPptrPage();
      await this.#page!.setRequestInterception(true);
      this.setAgentConfig();
      this.on('response-to-user', async (user, content: any, { duration, res }) => {
        if (res) {
          const { metadata } = res;
          const age = _.get(metadata, 'init.state.user_info.age');
          const region = _.get(metadata, 'init.state.user_info.region');
          const proc_state = _.get(metadata, 'init.state.proc_state', '售前');
          if (region) {
            this.#ctx.log?.debug('识别出region，跳过催付', user, region, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
          if (age && (age < 18 || age > 60)) {
            this.#ctx.log?.debug('识别到无法办理年龄，跳过催付', user, age, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
          if (proc_state !== '售前') {
            this.#ctx.log?.debug('识别到订单状态，跳过催付', user, proc_state, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
        }

        const msg = content
          .map((item) => (item.type === 'text' ? item.text[0].value : ''))
          .join(',');
        console.log('response-to-user', msg, user);

        if (duration) {
          this.#durations.push(duration);
          const sum = this.#durations.reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
          );
          const average = sum / this.#durations.length;
          this.#systemStatusManager.setAvgMyqaElapsed(average);
          this.#systemStatusManager.setMinMyqaElapsed(duration);
          this.#systemStatusManager.setMaxMyqaElapsed(duration);
        }
        // if (this.#userList[user].orderInfo.length) {
        //   await this.#expressTracker.formatKuadiContent(user, msg, {
        //     code: "",
        //     phone: "",
        //   });
        // }

        this.sendMessage(user, msg, 'txt');
      });

      // 记录超时与错误
      this.on('myqa-error', async (uid, err) => {
        this.#systemStatusManager.addMyqaError();
        if (err?.includes('超时')) {
          this.#systemStatusManager.addmyqaTimeout();
        }
      });

      // ctx.emit("update-status", {
      //   status: state,
      // });

      this.#page!.on('request', async (request) => {
        try {
          if (this.#agentStatue === 'running' || this.#isSilence) {
            const data = getCurrentUserMessageApi(request, this.#isSilence, () => {
              this.#systemStatusManager.addSendMsgCount();
            });
            if (data) {
              await this.userMsgCreate(data);
            }
          }
          // 设置催付
          setupReminders(
            request,
            this.#agentConfig,
            this.#isSilence,
            this.#userList,
            this.#ctx.log,
            (id, userList) => {
              this.#userList[id] = userList;
            },
            (uid, content, type) => {
              this.sendMessage(uid, content, type, 'rush');
            }
          );
          if (request.url().includes('/backstage/uponlineservice')) {
            const postData = JSON.parse(request.postData() ?? '{}');
            if (Object.keys(postData).length) {
              this.#ctx.emit('update-status', {
                status: hfbStatusMap.get(postData.status)
              });
              this.#ctx.log?.debug('客服状态切换', postData);
              this.sendMessageToOpener('APP_CHANGED_EVENT', {
                type: 'status',
                status: hfbStatusMap.get(postData.status)
              });
            }
          }

          request.continue();
        } catch (err) {
          request.continue();
        }
      });

      // 启动页面时监听所有响应
      this.#page!.on('response', async (response) => {
        const url = response.url();
        // 获取当前用户订单信息
        if (url.includes('/backstage/cmpoent/order/query')) {
          try {
            const jsonResponse = await response.json();
            this.#ctx.log?.debug(
              jsonResponse?.data?.[0]?.user_id,
              '获取订单信息成功',
              jsonResponse?.data
            );
            if (jsonResponse.data.length > 0) {
              this.#userList[jsonResponse.data[0].user_id] = {
                orderInfo: jsonResponse.data,
                user_id: jsonResponse.data[0].user_id
              };
            }
          } catch (err) {
            //err
          }
        }

        // 获取当前用户店铺信息
        if (url.includes('/backstage/currentuser')) {
          try {
            const jsonResponse = await response.json();
            this.#shopInfo = jsonResponse?.data;
            this.#ctx.log?.debug('当前用户店铺信息', this.#shopInfo);
            const tabName = this.#shopInfo?.CustomerServiceInfo?.screen_name;
            if (tabName) {
              this.#webContentsView.webContents.executeJavaScript(`document.title = "${tabName}"`);
              this.sendMessageToOpener('APP_CHANGED_EVENT', {
                type: 'edit',
                data: {
                  name: tabName,
                  id: this.#config.id
                }
              });
            }
          } catch (err) {
            // err
          }
        }
        // 获取商品列表
        // if (url.includes('/backstage/workstation/get_product_list')) {
        //   try {
        //     const jsonResponse = await response.json();
        //     this.#goodsList = jsonResponse?.data;
        //     this.#ctx.log?.debug('获取商品信息成功');
        //     // 上传给myqa商品信息
        //     uploadGoodsInfo(
        //       formatGoodsList(this.#goodsList, this.#shopInfo.ShopName),
        //       this.#ctx,
        //       this.#shopInfo
        //     );
        //   } catch (err) {
        //     // err
        //   }
        // }

        // 获取当前客服状态
        if (url.includes('/backstage/getMainCustomerServiceInfo')) {
          try {
            const jsonResponse = await response.json();
            this.#ctx.log?.debug('获取当前客服状态信息', jsonResponse.data);
            // 抖店 0 小休(忙碌) 1 在线  2 离线
            const OnlineStatus = jsonResponse.data.OnlineStatus;
          } catch (err) {
            // err
          }
        }
      });

      // 监听页面导航事件
      this.#page!.on('framenavigated', async (frame) => {
        const url = frame.url();
        if (url.startsWith('https://fxg.jinritemai.com/ffa/mshop/homepage/index')) {
          const pigeonCid = await this.getPigeonCid();
          if (pigeonCid) {
            await this.#page!.goto(
              `https://im.jinritemai.com/pc_seller_v2/main/workspace?selfId=${pigeonCid}`,
              {
                waitUntil: 'networkidle0'
              }
            );
            // 切换执行状态
            await this.#page!.exposeFunction('updateAgentStatue', async (state) => {
              await updateAgentStatue(this.#page, state);
              if (state === 'running') {
                this.#systemStatusManager.start();
              }
              if (state === 'pause') {
                this.#systemStatusManager.pause();
              }
              this.#agentStatue = state;
            });

            // 获取当前客服数据
            await this.#page!.exposeFunction('currentReceptInfo', async () => {
              this.#currentReceptInfoInterval = setInterval(async () => {
                const data = await currentReceptInfo(this.#page);
                uploadBaseInfo(
                  data,
                  this.#ctx,
                  this.#shopInfo,
                  this.#systemStatusManager.getStatus()
                );
              }, 60 * 1000);
            });

            // 模拟点击页面切换按钮抓取商品信息
            await this.#page!.evaluate(() => {
              const pigeonChatScrollBox: HTMLDivElement | null = document.querySelector(
                "div[data-workstation-side-tab-key='商品']"
              );
              if (pigeonChatScrollBox) {
                pigeonChatScrollBox.click();
              }
            });

            if (!this.#isSilence) {
              // 注入运行状态切换按钮
              statusSwitchBtn(this.#page);
            }

            // 设置发送内容并发送
            await this.#page!.exposeFunction(
              'setTextAreaAndSend',
              async (uid, msg, agentConfig, type) => {
                await sendMessage(this.#page, agentConfig, uid, msg, type);
              }
            );

            // 切换锁
            await this.#page!.exposeFunction('toggleLock', async (state) => {
              await toggleLock(this.#page, state);
            });

            // 创建用户消息
            await this.#page!.exposeFunction('userMsgCreate', async (postData) => {
              await this.userMsgCreate(postData);
            });

            // 转交用户
            await this.#page!.exposeFunction('transferUser', async (uid) => {
              this.#systemStatusManager.addTransferCount();
              const error = await transferUser(this.#page, this.#agentConfig);
              if (error) {
                this.#ctx.log?.debug(
                  '阿根廷转人工失败报警(抖店)',
                  [
                    '### 阿根廷转人工失败报警(抖店)',
                    `> 客服: ${this.#userName}`,
                    `> uid: ${uid}`,
                    `> 问题: ${error}`
                  ].join('\n\n')
                );
              }
            });

            // 累计敏感词次数
            await this.#page!.exposeFunction('addSenceCount', async () => {
              this.#systemStatusManager.addSenceCount();
            });

            // 发送图片
            await this.#page!.exposeFunction('sendImage', async (imgurl) => {
              await sendImage(this.#page, imgurl, this.#ctx.log);
            });

            // 是否需要转接
            await this.#page!.exposeFunction('needTransfer', (uid, content) => {
              return needTransfer(uid, content, this.#ctx.log);
            });

            // 获取新进线的对话消息
            await getSessionMessage(this.#page);

            // 监听当前用户消息变化为接口监听兜底获取用户消息
            await getCurrentUesrMessage(this.#page, this.#isSilence);

            await this.#page!.evaluate(() => {
              (window as any).updateAgentStatue('pause');
              (window as any).currentReceptInfo();
            });
          }
        }
      });

      await this.#page!.goto('https://fxg.jinritemai.com/login/common', {
        waitUntil: 'networkidle0'
      });
    } catch (err) {
      console.error('Error in run method:', err);
      this.#systemStatusManager.addUnhandledRejectionCount();
    } finally {
      this.#runningPromise.resolve();
    }
  }

  async getPigeonCid(): Promise<string | undefined> {
    try {
      const uuid = await this.#page!.evaluate(() => {
        return JSON.parse(localStorage.getItem('__tea_sdk_ab_version_512225') ?? '{}')?.uuid;
      });

      return uuid;
    } catch (err) {
      this.#ctx.log?.error('跳转至客服页面失败', err);
      return undefined;
    }
  }

  async doStop() {
    try {
      await this.#runningPromise.promise;
      await this.#page!.close();
      clearInterval(this.#currentReceptInfoInterval);
    } catch (err) {
      console.error('Error in stop method:', err);
    } finally {
      this.#config = undefined;
      this.#page = undefined;
    }
  }
}

export default Doudian;

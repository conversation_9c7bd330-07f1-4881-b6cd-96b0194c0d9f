/* eslint-disable @typescript-eslint/no-explicit-any */
// src/Login.js
import React, { useState } from 'react';
import { Form, Input, Button, message } from 'antd';
import './index.css';
import { useNavigate } from 'react-router-dom';
import {
  // addAgentium,
  // getAdminAgentiumOne,
  // copyAdminAssistants,
  // getOrganizations,
  getAssistants,
  // getUserRelation,
  // loginOidc,
  // getMasterUser,
  userLogin
} from '@/api/myqaApi';
import icon_password from '@/assets/icons/icon_password.svg';
import icon_username from '@/assets/icons/icon_username.svg';
import icon_loginLeft from '@/assets//icons/login_left.svg';
import icon_muyan from '@/assets//icons/muyan_icon_white.svg';
// import axios from 'axios';
import packageJson from '../../../package.json';
import useIsDev from '@/hooks/useIsDev.ts';

const Icon = ({ src }: { src: string }) => {
  return <img src={src} alt="icon" className="login-input-icon" />;
};
const Login = (): React.ReactElement => {
  // const [loading, setLoading] = useState(false);
  const [loginLock, setLoginLock] = useState(false);
  const onFinish = async (values) => {
    try {
      const loginRes = await userLogin(values);
      console.log(loginRes);
      setLoginLock(true);
      const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
      console.log(accountData);
      localStorage.setItem(
        'accountData',
        JSON.stringify([
          ...accountData.filter((item) => {
            return item.username !== values.username;
          }),
          values
        ])
      );

      const userData = loginRes.data.user;
      localStorage.setItem('accessToken', loginRes.data.tokens.accessToken);
      localStorage.removeItem('roleCode');
      // loginRes.data.permissions.map((item: any) => {
      //   if (item.permission_code === 'myqaweb_login') {
      //     localStorage.setItem('roleCode', 'admin');
      //   }
      // });
      // window.ipcRenderer.send('set-accessToken', loginRes.data.tokens.access_token);
      localStorage.setItem('organization', userData.org_id);
      // window.ipcRenderer.send('set-orgId', userData.org_id);
      // window.ipcRenderer.send('set-myqaUser', {
      //   username: userData.user_name,
      //   id: userData.user_id,
      //   ...userData
      // });
      localStorage.setItem(
        'user',
        JSON.stringify({
          username: userData.user_name,
          id: userData.user_id,
          ...userData
        })
      );
      localStorage.setItem('configStates', JSON.stringify([]));
      setLoginLock(false);
      message.success('登录成功');
      window.xBrowser?.send('user-login', values.username);
      navigate('/home');
      // const assistantsRes = await getAssistants();
      // console.log(assistantsRes, 'assistantsRes');
      // if (assistantsRes.data.length === 0) {
      //   // initAssistants(data.user.id, localStorage.getItem("organization"));
      //   message.error('请联系管理员添加助手后再次登录');
      //   setLoginLock(false);
      // } else {
      //   const id = assistantsRes.data[0]?.id;
      //   localStorage.setItem('assistantId', id);
      //   localStorage.setItem(
      //     'recovery_mode',
      //     assistantsRes.data[0]?.metadata?.recovery_mode ?? 'rag'
      //   );
      //   // window.ipcRenderer.send('set-assistants', id);
      //   // Sentry.captureMessage('user login', {
      //   //   tags: { username: values.username }, // 附带的数据
      //   //   level: 'warning' // 错误等级，警告还是报错由你决定
      //   // });
      //   setLoginLock(false);
      //   message.success('登录成功');
      //   window.xBrowser?.send('user-login', values.username);
      //   navigate('/home');
      // }
    } catch (error) {
      setLoginLock(false);
    }
  };

  // try {
  //   setLoginLock(true);
  //   localStorage.setItem("username", values.username);
  //   await authentication({ uid_field: values.username });
  //   const res = await authentication({ password: values.password });
  //   if (res.data.response_errors) {
  //     message.error("账号或密码错误！");
  //     setLoginLock(false);
  //     return;
  //   }
  //   await myqaLogin({
  //     username: values.username,
  //     password: values.password,
  //   });
  // } catch (error) {
  //   setLoginLock(false);
  //   message.error("账号或密码错误！");
  // }

  // const myqaLogin = ({ username, password }): Promise<any> => {
  //   return loginOidc().then(async (res) => {
  //     try {
  //       const d = new URL(res.url);
  //       // urt地址中的参数不影响调用结果
  //       const flowsRes = await axios.get(
  //         `https://auth.dongchacat.cn/api/v3/flows/executor/default-provider-authorization-implicit-consent/?query=response_type%3Dcode%26client_id%3D5DJM8fhLTkJCQltIq1Vhpv44PvShTrI6Y66g7N52%26redirect_uri%3Dhttps%253A%252F%252Faikf.tuzhou.net%253A2001%252Fapi%252Fv1%252Fauth%252Foidc%26scope%3Dopenid%2Bemail%2Bprofile%26state%3D${d.searchParams.get('state')}%26nonce%3D${d.searchParams.get('nonce')}`
  //       );
  //       const toRes = await axios.get(flowsRes.data.to);
  //       const { data } = toRes;
  //
  //       const masterData = await getMasterUser(data.secret_key.id);
  //       // const organ = await getOrganizations(masterData.data.master_user_sk);
  //       const rtres = await getUserRelation();
  //       const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
  //       localStorage.setItem(
  //         'accountData',
  //         JSON.stringify([
  //           ...accountData.filter((item) => {
  //             return item.username !== username;
  //           }),
  //           {
  //             username,
  //             password
  //           }
  //         ])
  //       );
  //       const orgId = masterData?.data.master_org_id ?? null;
  //
  //       //  const orgId =organ?.data?.find((item) => item.user_id === data.user.id)?.id ?? null;
  //
  //       const rtresItem = rtres.data?.data?.find((item) => item.fid === username);
  //       if (rtresItem) {
  //         localStorage.setItem('organization', rtresItem.oid);
  //         window.ipcRenderer.send('set-orgId', rtresItem.oid);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem(
  //           'user',
  //           JSON.stringify({
  //             username: '',
  //             ...data.user,
  //             masterUserId: masterData.data.master_user_id
  //           })
  //         );
  //       } else {
  //         localStorage.setItem('organization', orgId);
  //         window.ipcRenderer.send('set-orgId', orgId);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem(
  //           'user',
  //           JSON.stringify({
  //             username: '',
  //             ...data.user,
  //             masterUserId: masterData.data.master_user_id
  //           })
  //         );
  //       }
  //       // localStorage.setItem("secret_key", data.secret_key.id);
  //       localStorage.setItem('secret_key', masterData.data.master_user_sk);
  //       localStorage.setItem('old_secret_key', data.secret_key.id);
  //       window.ipcRenderer.send('set-secretKey', masterData.data.master_user_sk);
  //       localStorage.setItem('configStates', JSON.stringify([]));
  //       const assistantsRes = await getAssistants();
  //       if (assistantsRes.data.length === 0) {
  //         // initAssistants(data.user.id, localStorage.getItem("organization"));
  //         message.error('请联系管理员添加助手后再次登录');
  //         setLoginLock(false);
  //       } else {
  //         const id = assistantsRes.data[0]?.id;
  //         localStorage.setItem('assistantId', id);
  //         localStorage.setItem(
  //           'recovery_mode',
  //           assistantsRes.data[0]?.metadata?.recovery_mode ?? 'rag'
  //         );
  //         window.ipcRenderer.send('set-assistants', id);
  //         message.success('登录成功');
  //
  //         Sentry.captureMessage('user login', {
  //           tags: { username: username }, // 附带的数据
  //           level: 'warning' // 错误等级，警告还是报错由你决定
  //         });
  //         navigate('/home');
  //         setLoginLock(false);
  //       }
  //       return { res };
  //     } catch (e) {
  //       message.error('登录失败');
  //       setLoginLock(false);
  //     }
  //   });
  // };

  // const initAssistants = async (user_id, org_id): Promise<void> => {
  //   try {
  //     setLoading(true);
  //     const res: any = await copyAdminAssistants(user_id, org_id);
  //     const ares = await getAdminAgentiumOne(
  //       "asst_roj2hdhfw2r22e4mhodjts7afhk76a5y",
  //     );
  //     const metadata = ares.data?.data?.[0]?.meta_data ?? {};
  //     metadata.secret_key = localStorage.getItem("secret_key");
  //     metadata.assistant_id = res.id;
  //     await addAgentium({
  //       version: "",
  //       name: "agentium.json",
  //       assistant_id: res.id,
  //       meta_data: ares.data?.data?.[0]?.meta_data ?? {},
  //     });
  //     message.success("登录成功");
  //     navigate("/home");
  //     setLoginLock(false);
  //     localStorage.setItem("assistantId", res.id);
  //     window.ipcRenderer.send("set-assistants", res.id);
  //   } catch (e) {
  //     message.error("初始化配置失败!");
  //     setLoading(false);
  //   }
  // };
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const isDev = useIsDev();

  return (
    <div className="login-container">
      <img src={icon_muyan} className="muyanIcon" />
      {isDev && <span className="dev">DEV</span>}
      <img src={icon_loginLeft} />
      <div className="login-box">
        <div className="login-content">
          <div className="login-title">
            <div>Hi～</div>
            <div>欢迎使用牧言·智能客服</div>
          </div>
          <Form name="normal_login" form={form} className="login-form" onFinish={onFinish}>
            <Form.Item name="username" rules={[{ required: true, message: '请输入用户名!' }]}>
              <Input
                prefix={
                  <div
                    style={{
                      marginRight: '16px',
                      paddingTop: '2px',
                      lineHeight: '100%'
                    }}
                  >
                    <Icon src={icon_username} />
                  </div>
                }
                className="login-input"
                placeholder="用户名"
              />
            </Form.Item>
            <Form.Item name="password" rules={[{ required: true, message: '请输入密码!' }]}>
              <Input.Password
                prefix={
                  <div
                    style={{
                      marginRight: '16px',
                      paddingTop: '2px',
                      lineHeight: '100%'
                    }}
                  >
                    <Icon src={icon_password} />
                  </div>
                }
                className="login-input"
                placeholder="密码"
                onPressEnter={() => form.submit()}
              />
            </Form.Item>
            <Form.Item>
              <Button
                loading={loginLock}
                type="primary"
                onClick={() => {
                  form.submit();
                }}
                style={{
                  width: '100%',
                  height: '35px'
                }}
              >
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
      {/* <Spin spinning={loading} tip="初始化中..." fullscreen /> */}
      <div className="version">Version {packageJson ? packageJson.version : '-'}</div>
    </div>
  );
};

export default Login;

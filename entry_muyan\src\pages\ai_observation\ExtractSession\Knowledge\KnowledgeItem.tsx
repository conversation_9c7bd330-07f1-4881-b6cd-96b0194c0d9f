import { observer } from 'mobx-react-lite';
import icon_cancel from '@/assets/icons/icon_cancel.svg';
import icon_add from '@/assets/icons/icon_addy.svg';
import { KnowledgeItemEdit } from './KnowledgeItemEdit';
import { useState } from 'react';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import { store } from '@/store';
import cloneDeep from 'lodash-es/cloneDeep';
type Props = {
  item: any;
  search: string;
  kb_name: string;
};
export const KnowledgeItem = observer(({ item, search, kb_name }: Props) => {
  const [open, setOpen] = useState(false);
  const [type, setType] = useState('');
  const [data, setData] = useState({});
  const onOpenChange = (val: boolean) => {
    setOpen(val);
    if (!val) setType('');
  };
  const { knowledge, extractSession } = store;
  const deleteItem = () => {
    ConfirmationDialog({ title: '是否移除此条?' }).then(() => {
      if (extractSession.newIntention.includes(item.intent_id)) {
        knowledge.deleteProductQa(item.id).then(() => {
          knowledge.loadDomain(kb_name, knowledge.versionId, search).then((res) => {
            knowledge.setList(res.data);
          });
          const arr: any[] = cloneDeep(extractSession.editList);
          item.query.forEach((item) => {
            const index = arr.map((item) => item.id).indexOf(item.id);
            if (index !== -1) arr.splice(index, 1);
          });
          const newIntention: any[] = cloneDeep(extractSession.newIntention);
          const index = newIntention.indexOf(item.intent_id);
          const intentionList = cloneDeep(extractSession.intentionList);
          const indexIntention = intentionList.indexOf(item.intent_id);
          newIntention.splice(index, 1);
          intentionList.splice(indexIntention, 1);
          extractSession.setEditList(arr);
          extractSession.setNewIntention(newIntention);
          extractSession.setCurrentIntentionId('');
          extractSession.setIntentionList(intentionList);
          extractSession.setCurrenQuerytList(
            knowledge.list
              .filter((item) => intentionList.includes(item.intent_id))
              .map((item) => item.query)
              .flat()
              .map((item) => item.query)
          );
          extractSession.setCurrentIntentionId('');
          extractSession.setCurrenQuerytList([]);
        });
      } else {
        const arr: any[] = cloneDeep(extractSession.editList);
        item.query.forEach((item) => {
          if (arr.map((item) => item.id).includes(item.id)) deleteProductQuery(item.id);
        });
        const intentionList = cloneDeep(extractSession.intentionList);
        const indexIntention = intentionList.indexOf(item.intent_id);
        intentionList.splice(indexIntention, 1);
        extractSession.setIntentionList(intentionList);
        extractSession.setCurrenQuerytList(
          knowledge.list
            .filter((item) => intentionList.includes(item.intent_id))
            .map((item) => item.query)
            .flat()
            .map((item) => item.query)
        );
        extractSession.setCurrentIntentionId('');
        extractSession.setCurrenQuerytList([]);
      }
    });
  };
  const deleteReply = (id) => {
    ConfirmationDialog({ title: '是否删除此条回复?' }).then(() => {
      knowledge.deleteResponse(id).then(() => {
        knowledge.loadDomain(kb_name, knowledge.versionId).then((res) => {
          knowledge.setList(res.data);
        });
        const arr = cloneDeep(extractSession.editList);
        const index = arr.map((item) => item.id).indexOf(id);
        arr.splice(index, 1);
        extractSession.setEditList(arr);
      });
    });
  };
  const deleteQuestion = (id) => {
    ConfirmationDialog({ title: '是否删除此条问法?' }).then(() => {
      deleteProductQuery(id);
    });
  };

  const deleteProductQuery = (id) => {
    knowledge.deleteProductQuery(id).then(() => {
      knowledge.loadDomain(kb_name, knowledge.versionId).then((res) => {
        knowledge.setList(res.data);
      });
      const arr = cloneDeep(extractSession.editList);
      const index = arr.map((item) => item.id).indexOf(id);
      arr.splice(index, 1);
      extractSession.setEditList(arr);
    });
  };

  return (
    <div>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          fontSize: '14px',
          overflow: 'hidden',
          alignItems: 'center',
          paddingRight: '12px'
        }}
      >
        <div
          style={{
            width: '15px',
            height: '15px',
            marginRight: '3px',
            marginLeft: '3px',
            cursor: 'pointer'
          }}
        >
          {(extractSession.newIntention.includes(item.intent_id) ||
            extractSession.intentionList.includes(item.intent_id)) && (
            <img
              style={{
                width: '15px',
                height: '15px',
                cursor: 'pointer'
              }}
              src={icon_cancel}
              alt="icon"
              onClick={deleteItem}
            />
          )}
        </div>
        <div
          style={{
            minHeight: '100px',
            borderRadius: '4px',
            display: 'flex',
            justifyContent: 'space-between',
            padding: '3px 10px',
            alignItems: 'center',
            flex: 1,
            ...(extractSession.currentIntentionId === item.intent_id
              ? {
                  background: '#f6f6f7'
                }
              : {})
          }}
          className="knowledgeHover"
        >
          <div
            style={{
              flex: 1,
              marginRight: '6px',
              overflow: 'hidden'
            }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                marginRight: '6px',
                marginLeft: '6px',
                alignItems: 'center'
              }}
            >
              <div
                style={{
                  maxWidth: '300px',
                  flex: 1,
                  cursor: 'pointer',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                onClick={() => {
                  setOpen(true);
                  setType('termsUpdate');
                  setData({
                    id: item.id,
                    value: item.terms,
                    intent_id: item.intent_id,
                    title: '意图添加'
                  });
                }}
              >
                {item.terms}
                {!item.terms && extractSession.newIntention.includes(item.intent_id) && (
                  <img
                    style={{
                      width: '15px',
                      height: '15px',
                      marginLeft: '50%',
                      cursor: 'pointer'
                    }}
                    src={icon_add}
                    alt="icon"
                  />
                )}
              </div>
              <div></div>
            </div>
          </div>
          <div
            style={{
              flex: 1,
              marginRight: '6px',
              overflow: 'hidden'
            }}
          >
            {item.response.map((val) => (
              <div
                key={val.id}
                style={{
                  display: 'flex',
                  marginTop: '6px',
                  marginBottom: '6px',
                  alignItems: 'center'
                }}
              >
                <img
                  style={{
                    width: '15px',
                    height: '15px',
                    marginRight: '8px'
                  }}
                  src={icon_cancel}
                  onClick={() => deleteReply(val.id)}
                  alt="icon"
                />
                <div
                  style={{
                    maxWidth: '300px',
                    flex: 1,
                    cursor: 'pointer',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                  onClick={() => {
                    setOpen(true);
                    setType('responseUpdate');
                    setData({
                      id: val.id,
                      value: val.response,
                      response_id: val.response_id,
                      responseItem: val,
                      title: '回复编辑'
                    });
                  }}
                >
                  {val.response}
                </div>
              </div>
            ))}
            <img
              style={{
                width: '15px',
                height: '15px',
                marginLeft: '50%',
                transform: 'translate(-8px)',
                bottom: '-20px',
                cursor: 'pointer'
              }}
              src={icon_add}
              onClick={() => {
                setType('responseAdd');
                setOpen(true);
                setData({
                  title: '回复添加'
                });
              }}
              alt="icon"
            />
          </div>
          <div
            style={{
              flex: 1,
              marginRight: '6px',
              overflow: 'hidden'
            }}
          >
            {item?.query.map((val) => (
              <div
                key={val.id}
                style={{
                  display: 'flex',
                  marginLeft: '6px',
                  marginRight: '6px',
                  alignItems: 'center'
                }}
              >
                <div
                  style={{
                    width: '18px',
                    height: '18px',
                    marginRight: '8px'
                  }}
                >
                  <img
                    src={icon_cancel}
                    alt="icon"
                    style={{
                      width: '15px',
                      height: '15px',
                      cursor: 'pointer'
                    }}
                    onClick={() => deleteQuestion(val.id)}
                  />
                </div>
                <div
                  style={{
                    maxWidth: '300px',
                    cursor: 'pointer',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                  onClick={() => {
                    setType('queryUpdate');
                    setOpen(true);
                    setData({
                      id: val.id,
                      query_id: val.query_id,
                      value: val.query,
                      title: '问法编辑'
                    });
                  }}
                >
                  {val.query}
                </div>
              </div>
            ))}
            <img
              style={{
                width: '15px',
                height: '15px',
                marginLeft: '50%',
                transform: 'translate(-8px)',
                bottom: '-20px',
                cursor: 'pointer'
              }}
              src={icon_add}
              onClick={() => {
                setType('queryAdd');
                setOpen(true);
                setData({
                  title: '问法添加'
                });
              }}
              alt="icon"
            />
          </div>
        </div>
      </div>
      <KnowledgeItemEdit
        open={open}
        product_qa_id={item.intent_id}
        data={data}
        onOpenChange={onOpenChange}
        type={type}
        kb_name={kb_name}
      />
    </div>
  );
});

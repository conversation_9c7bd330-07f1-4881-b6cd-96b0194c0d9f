/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, type ReactNode } from 'react';
import ReactJson from 'react-json-view';
import Modal from './Modal';
export const NodeContent: React.FC<{
  jsonData: any;
  children: ReactNode;
  title: string;
  disabled: boolean;
}> = ({ jsonData, children, title, disabled }) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      {disabled ? (
        <>{children}</>
      ) : (
        <>
          <span
            onClick={() => {
              setOpen(true);
            }}
          >
            {children}
          </span>
          <Modal open={open} footer={false} setOpen={setOpen} title={title}>
            <div
              style={{
                overflowY: 'auto',
                height: 'calc(100vh - 300px)',
                padding: '10px 20px'
              }}
            >
              <ReactJson src={jsonData} />
            </div>
          </Modal>
        </>
      )}
    </>
  );
};

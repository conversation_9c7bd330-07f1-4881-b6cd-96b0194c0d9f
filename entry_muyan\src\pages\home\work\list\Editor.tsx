import { Button, message, Modal } from 'antd';
import { JsonEditor } from 'jsoneditor-react';
import 'jsoneditor-react/es/editor.min.css';
import { useEffect, useRef, useState } from 'react';
import { getConfigDetail, updateConfigDetail } from '@/api/myqaApi.ts';
import style from './index.module.css';
import { Differ } from '@/pages/home/<USER>/list/Differ.tsx';

export function Editor({
  id,
  name,
  open,
  configContent,
  close
}: {
  id: string;
  configContent: string;
  name: string;
  open: boolean;
  close: () => void;
}) {
  const [originalConfig, setOriginalConfig] = useState<{}>({});
  const [config, setConfig] = useState<{}>({});
  const [validate, setValidate] = useState<boolean>(false);
  const [saveLoading, setSaveLoading] = useState<boolean>(false);
  const jsonEditorRef = useRef();
  const [showDiff, setShowDiff] = useState<boolean>(false);

  useEffect(() => {
    if (!open) return;
    setShowDiff(false);

    const conf = JSON.parse(configContent);
    setTimeout(() => {
      setConfig(conf);
      // @ts-ignore jsonEditor
      const editor = jsonEditorRef && jsonEditorRef.current && jsonEditorRef.current.jsonEditor;
      if (editor && conf) {
        // @ts-ignore update
        editor.update(conf);
      }
    }, 0);
  }, [id, open, configContent]);
  const handleValidationError = (err) => {
    if (err.length) {
      setValidate(false);
    } else {
      setValidate(true);
    }
    console.log('handleValidationError', err);
  };

  const save = () => {
    setSaveLoading(true);
    if (!showDiff) {
      setSaveLoading(false);
      setShowDiff(true);
      setOriginalConfig(JSON.parse(configContent));
    } else {
      updateConfigDetail({
        id: id,
        configMetaData: config
      })
        .then((res) => {
          setSaveLoading(false);
          console.log('updateConfigDetail', res);
          close();
          message.success('保存成功');
        })
        .catch(() => {
          setSaveLoading(false);
        });
    }
  };

  return (
    <Modal
      title={`编辑 ${name}(${id})`}
      width={800}
      open={open}
      classNames={{
        footer: style.editor_footer
      }}
      footer={[
        <Button key="cancel" onClick={() => (showDiff ? setShowDiff(false) : close())}>
          {!showDiff ? '取消' : '上一步'}
        </Button>,
        <Button
          key="submit"
          type="primary"
          disabled={!validate}
          loading={saveLoading}
          onClick={save}
        >
          {!showDiff ? '保存' : '确认'}
        </Button>
      ]}
      onCancel={close}
    >
      {showDiff ? (
        <div
          style={{
            height: 'calc(-300px + 100vh)',
            minHeight: '278px',
            overflow: 'scroll'
          }}
        >
          <Differ left={originalConfig} right={config} />
        </div>
      ) : (
        <JsonEditor
          ref={jsonEditorRef}
          value={config}
          onChange={(val) => setConfig(val)}
          onError={(...args) => console.log('err', ...args)}
          onValidationError={handleValidationError}
          mode="code"
          htmlElementProps={{ style: { height: 'calc(100vh - 300px)', minHeight: 278 } }}
        />
      )}
    </Modal>
  );
}

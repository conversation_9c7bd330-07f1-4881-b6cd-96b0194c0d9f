/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
import { useEffect, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Image, Upload, message, Spin, Modal } from 'antd';
import type { UploadFile } from 'antd';
// import { uploadFile } from "./utils/file-uploader-minio";
import { uploadFile } from './utils/file-uploader-oss';

import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { LoadingOutlined } from '@ant-design/icons';

const getBase64 = (file: any): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

type Props = {
  value: any[];
  onChange: (files: UploadFile[]) => void;
};
export const PlatformInfoEditUploadImg = ({ value, onChange }: Props) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [files, setFiles] = useState<UploadFile[]>([]);
  const [loading, setLoading] = useState(false);
  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as any);
    }
    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
  };
  useEffect(() => {
    setFiles(
      value.map((item) => {
        item.url = getPreviewImage(item.url ?? '');
        return item;
      })
    );
    setFileList(
      value.map((item) => {
        item.url = getPreviewImage(item.url ?? '');
        return item;
      })
    );
  }, [value]);

  const handleChange = ({ file, fileList: newFileList }) => {
    const fileType = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!fileType.includes(file.type ?? '')) {
      message.warning('只能上传 JPG/PNG/GIF/WebP 格式的图片文件。');
      return;
    }
    const isAdd = newFileList.find((item) => item.uid === file.uid);
    if (isAdd) {
      setLoading(true);
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target) {
          // const binaryData = event.target.result;
          const uid = uuidv4();
          uploadFile(
            // binaryData,
            // "rpa",
            `img/${dayjs(new Date()).format('YYYY-MM-DD')}/${uid}.${file?.name.split('.').at(-1)}`,
            file
          )
            .then((res: any) => {
              const newFile = {
                uid: file.uid,
                name: file.name,
                type: file.type,
                url: res.url
              };
              onChange([...files, newFile]);
              setFiles([...files, newFile]);
            })
            .finally(() => {
              setLoading(false);
            });
        }
      };
      reader.onerror = (error) => {
        console.error('Error reading file:', error);
      };
      reader.readAsArrayBuffer(file); // 开始读取文件内容为二进制数组缓冲区
    } else {
      const newFiles = files.filter((item) => item.uid !== file.uid);
      setFiles(newFiles);
      onChange(newFiles);
      setFileList(newFileList);
    }
  };

  const uploadButton = (
    <button style={{ border: 0, background: 'none' }} type="button">
      <PlusOutlined style={{ color: '#2E74FF', fontSize: 24 }} />
    </button>
  );

  const beforeUpload = () => {
    return false;
  };

  const handleAfterClose = (e) => {
    e.stopPropagation(); // 阻止事件向上冒泡
  };

  const getPreviewImage = (url: string): string => {
    const index = url.indexOf('/rpa');
    if (index !== -1) {
      return 'https://muyu-public.oss-cn-beijing.aliyuncs.com' + url.slice(index);
    }
    return url;
  };

  return (
    <>
      <Upload
        listType="picture-card"
        style={{ width: '64px', height: '64px' }}
        fileList={fileList}
        onPreview={handlePreview}
        onChange={handleChange}
        beforeUpload={beforeUpload}
      >
        {fileList.length >= 1 ? null : loading ? (
          <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
        ) : (
          uploadButton
        )}
      </Upload>
      {previewImage && (
        <Modal open={true} closable={false} footer={null} onCancel={handleAfterClose}>
          <Image
            wrapperStyle={{ display: 'none' }}
            preview={{
              visible: previewOpen,
              onVisibleChange: (visible) => setPreviewOpen(visible),
              afterOpenChange: (visible) => !visible && setPreviewImage('')
            }}
            src={previewImage}
          />
        </Modal>
      )}
    </>
  );
};

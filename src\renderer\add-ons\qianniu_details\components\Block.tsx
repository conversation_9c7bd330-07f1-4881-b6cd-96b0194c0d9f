import dayjs from 'dayjs';
import copy from '../img/copy.svg';
import check from '../img/check.svg';
// import ClipboardJS from 'clipboard';
// import { useEffect } from 'react';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { useEffect, useRef, useState } from 'react';
import { Tooltip } from 'antd';

export default function Block({
  msg,
  selected
}: {
  msg: { id: string; answer: string; userMsgs: { question: string; time: string }[] };
  selected: boolean;
}) {
  const block = useRef<HTMLDivElement>(null);
  // const img = useRef<HTMLImageElement>(null);
  const imgs = useRef<any>({});
  // const [copied, setCopied] = useState(false);
  const [copies, setCopies] = useState({});

  const handleCopy = (type, key) => () => {
    console.log('handleCopy', type, key);
    if (type === 'image' && imgs.current[key]) {
      const dom = imgs.current[key];
      const canvas = document.createElement('canvas');
      canvas.width = dom.naturalWidth;
      canvas.height = dom.naturalHeight;
      const context = canvas.getContext('2d');
      context!.drawImage(dom, 0, 0);
      canvas.toBlob((blob) => {
        navigator.clipboard
          .write([
            new ClipboardItem({
              [blob!.type]: blob!
            })
          ])
          .then(() => {
            console.log('Copied');
            setCopies((old) => {
              return {
                ...old,
                [key]: true
              };
            });
            // setCopied(true);
            setTimeout(() => {
              setCopies((old) => {
                return {
                  ...old,
                  [key]: false
                };
              });
            }, 1500); // 复制成功后，提示状态持续1.5秒
          });
      });
    } else {
      setCopies((old) => {
        return {
          ...old,
          [key]: true
        };
      });
      // setCopied(true);
      setTimeout(() => {
        setCopies((old) => {
          return {
            ...old,
            [key]: false
          };
        });
      }, 1500); // 复制成功后，提示状态持续1.5秒
    }
  };

  useEffect(() => {
    if (selected && block.current) {
      block.current.scrollIntoView();
    }
  }, [selected]);
  // useEffect(() => {
  //   const clipboard = new ClipboardJS('.copy');
  //   // clipboard.on('success', (e) => {
  //   //   e.clearSelection();
  //   // });
  // }, []);

  const { answer, userMsgs } = msg;
  const ans = JSON.parse(answer);
  return (
    <div ref={block} className={selected ? 'block selected' : 'block'}>
      <div className="answer">
        <div className="left">原始回复</div>
        <div className="right">
          <div className="content-wrapper">
            {ans.map((a) => {
              if (a.type === 'txt') {
                return (
                  <p key={a.data}>
                    {a.data}
                    <CopyToClipboard text={a.data} onCopy={handleCopy('txt', a.data)}>
                      <Tooltip
                        title={!copies[a.data] ? '复制' : '已复制'}
                        overlayInnerStyle={{ borderRadius: 4 }}
                      >
                        <span className="copy">
                          {copies[a.data] ? (
                            <img src={check} alt="check" />
                          ) : (
                            <img src={copy} alt="copy" />
                          )}
                          {/*<img src={copy} alt="copy" />*/}
                        </span>
                      </Tooltip>
                    </CopyToClipboard>
                  </p>
                );
              } else if (a.type === 'image') {
                return (
                  <p key={a.data.url}>
                    <img
                      ref={(ref) => (imgs.current[a.data.url] = ref)}
                      crossOrigin="anonymous"
                      className="preview"
                      src={a.data.url}
                      alt="image"
                    />
                    <CopyToClipboard text={null} onCopy={handleCopy('image', a.data.url)}>
                      <Tooltip
                        title={!copies[a.data.url] ? '复制' : '已复制'}
                        overlayInnerStyle={{ borderRadius: 4 }}
                      >
                        <span className="copy">
                          {copies[a.data.url] ? (
                            <img src={check} alt="check" />
                          ) : (
                            <img src={copy} alt="copy" />
                          )}
                          {/*<img src={copy} alt="copy" />*/}
                        </span>
                      </Tooltip>
                    </CopyToClipboard>
                  </p>
                );
              } else {
                return null;
              }
            })}
          </div>

          {/*{answer.startsWith('image|') ? (*/}
          {/*  <img*/}
          {/*    ref={img}*/}
          {/*    crossOrigin="anonymous"*/}
          {/*    className="preview"*/}
          {/*    src={answer.replace('image|', '')}*/}
          {/*    alt="image"*/}
          {/*  />*/}
          {/*) : (*/}
          {/*  answer*/}
          {/*)}*/}
          {/*<CopyToClipboard text={answer} onCopy={handleCopy}>*/}
          {/*  <Tooltip title={!copied ? '复制' : '已复制'} overlayInnerStyle={{ borderRadius: 4 }}>*/}
          {/*    <span className="copy">*/}
          {/*      {copied ? <img src={check} alt="check" /> : <img src={copy} alt="copy" />}*/}
          {/*      /!*<img src={copy} alt="copy" />*!/*/}
          {/*    </span>*/}
          {/*  </Tooltip>*/}
          {/*</CopyToClipboard>*/}
        </div>
      </div>
      <div className="questions">
        <div className="left">买家问题</div>
        <div className="right">
          {userMsgs.length
            ? userMsgs.map((item, index) => {
                return (
                  <div className="question" key={index}>
                    <div className="content">
                      {item.question.startsWith('image|') ? (
                        <img
                          className="preview"
                          src={item.question.replace('image|', '')}
                          alt="image"
                        />
                      ) : (
                        item.question
                      )}
                    </div>
                    <div className="time">{dayjs(+item.time).format('YYYY-MM-DD HH:mm:ss')}</div>
                  </div>
                );
              })
            : '无'}
        </div>
      </div>
    </div>
  );
}

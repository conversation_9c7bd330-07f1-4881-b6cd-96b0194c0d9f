type ListState = {
  id: number | string;
  state: 'human' | 'ai' | 'error' | null;
  status?: string;
};
export const useConfigStates = (): {
  listState: ListState[];
  updateListState: (type: 'add' | 'update' | 'delete', newListState: ListState) => void;
  updateListStatus: (type: 'update', id: string, status: string) => void;
} => {
  const listState: ListState[] = JSON.parse(localStorage.getItem('configStates') ?? '[]');
  const updateListState = (type: 'add' | 'update' | 'delete', newListState: ListState): void => {
    if (type === 'add') {
      localStorage.setItem(
        'configStates',
        JSON.stringify([...JSON.parse(localStorage.getItem('configStates') ?? '[]'), newListState])
      );
    } else if (type === 'delete') {
      localStorage.setItem(
        'configStates',
        JSON.stringify(
          JSON.parse(localStorage.getItem('configStates') ?? '[]').filter(
            (item) => item.id !== newListState.id
          )
        )
      );
    } else {
      localStorage.setItem(
        'configStates',
        JSON.stringify(
          JSON.parse(localStorage.getItem('configStates') ?? '[]').map((item) =>
            item.id === newListState.id ? newListState : item
          )
        )
      );
    }
  };

  const updateListStatus = (type: 'update', id: string, status: string): void => {
    if (type === 'update') {
      localStorage.setItem(
        'configStates',
        JSON.stringify(
          JSON.parse(localStorage.getItem('configStates') ?? '[]').map((item) => {
            if (item.id === id) {
              item.status = status;
            }
            return item;
          })
        )
      );
    }
  };

  return { listState, updateListState, updateListStatus };
};

import style from './index.module.css';
import icon_work from '@/assets/icons/icon_work.svg';
// import icon_ai from '@/assets/icons/icon_ai.svg';
// import icon_date from '@/assets/icons/icon_date.svg';
import icon_work_ac from '@/assets/icons/icon_work_ac.svg';
// import icon_ai_ac from '@/assets/icons/icon_ai_ac.svg';
// import icon_date_ac from '@/assets/icons/icon_date_ac.svg';
// import icon_ai_set from '@/assets/icons/icon_ai_set.svg';
// import icon_ai_set_ac from '@/assets/icons/icon_ai_set_ac.svg';
// import icon_settings from '@/assets/icons/icon_settings.svg';
// import icon_settings_ac from '@/assets/icons/icon_settings_ac.svg';
// import icon_manual from '@/assets/icons/icon_manual.svg';
// import icon_manual_ac from '@/assets/icons/icon_manual_ac.svg';
import Item from './MenuItem';
// import { store } from "../../store";
import { observer } from 'mobx-react-lite';
const Menu = observer((): React.ReactElement => {
  // const { userInfo } = store;
  const menuList = [
    {
      src: icon_work,
      acSrc: icon_work_ac,
      text: '智能体',
      to: '/home',
      point: 'workspace'
    }
    // {
    //   src: icon_ai,
    //   acSrc: icon_ai_ac,
    //   text: 'AI观测',
    //   to: '/aiobservation',
    //   point: 'aiObserve'
    // },
    // {
    //   src: icon_manual,
    //   acSrc: icon_manual_ac,
    //   text: '人工回复',
    //   to: '/manual',
    //   point: 'aiManual'
    // },
    // {
    //   src: icon_ai_set,
    //   acSrc: icon_ai_set_ac,
    //   text: '品牌信息',
    //   to: '/aisetup',
    //   point: 'aiConfig'
    // },
    // {
    //   src: icon_date,
    //   acSrc: icon_date_ac,
    //   text: '数据',
    //   to: '/data',
    //   point: 'dataAnalysis'
    // },
    // {
    //   src: icon_settings,
    //   acSrc: icon_settings_ac,
    //   text: '设置',
    //   to: '/settings',
    //   point: 'accountManage'
    // }
  ];
  return (
    <div className={style.menu}>
      {menuList
        // .filter((item) => {
        //   return userInfo.permission.split(",").includes(item.point);
        // })
        .map((item) => (
          <Item key={item.to} acSrc={item.acSrc} to={item.to} src={item.src}>
            {item.text}
          </Item>
        ))}
      <div className={style.footer}></div>
    </div>
  );
});

export default Menu;

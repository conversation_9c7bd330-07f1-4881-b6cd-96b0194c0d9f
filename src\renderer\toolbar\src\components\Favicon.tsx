import React from 'react';
import { LoadingOutlined } from '@ant-design/icons';

const defaultFavicon =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAACIElEQVR42mIgBYwCAKtVrQBFDERxhz/BrUE6GrTDWd/NLFrS4O7OF+DyCTglbhUfQIO7Mw/N5TYydxTvZDcZffOSTKl5SUGXo0w9Y3xkfAN+/36Gd1jz3x0vKaudUarg5HsIsBZ7unac58snxzm98jq0AHthoyPnSa5WRLn62mD4I+M6G9+XlIrioq7598EoozdNQcAGbMmdo7d6Npl6EeVUrl+/vp+5PirqhZ5qfIPN4LKbmXOG55cvX97ftoffH/e1AzaD2oG+Gc63NK3jSvThd6sYp7j8L0M54We7kbnFeS92erETYjqnA+Oj99xW9rSsp4kcGyNqFZnWnlHpaNM+kVMDjWIFFdNHDWy3Viqno90EAF9tRiGl2qLrLq6gOnFGZ3Qwyx8LgnjWFMBHjXz7pMIV52q1IIB2HujCA4WTyzaN4CA+2clX3dOFyRmAFTm9Zic3udwn4pLmtCsoLeL3T4w973hk10KytUn4Zm2BCLk6y6M6rKUV69cPYGejkkLNYLJNret6SMPkfLSSUArOeF1Ii0BsCwmNMZQip2fI0uUcI22Q/LJHiGQAAX1j6xEiQ4qFSMqlE23OIeeQdZsUG+c67fI7pJec0SMdabVsnEO2z5uHkeg4NoERDBcn2iI5ji0XEnkAKDsyl19IjCuZNACwHYRDzwVXMumltAJRr7PQ7EjZGWT7t8hcZ/zYT9yN0oFvltO/YzIkumajAAB8rxc5irDSPwAAAABJRU5ErkJggg==';

const Favicon: React.FC<{
  url: string | undefined;
  isLoading: boolean;
}> = ({ url, isLoading }) => {
  return (
    <>
      <img
        src={url || defaultFavicon}
        onError={(e) => {
          e.currentTarget.src = defaultFavicon;
        }}
        onLoad={(e) => {
          e.currentTarget.style.visibility = 'visible';
        }}
        referrerPolicy="no-referrer"
      />
      {isLoading && (
        <LoadingOutlined
          style={{
            fontSize: '18px'
          }}
        />
      )}
    </>
  );
};

export default Favicon;

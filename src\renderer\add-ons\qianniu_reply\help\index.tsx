/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Steps } from 'antd';
import qlStep1Img from '../img/ql-step-1.png';
import qlStep2Img from '../img/ql-step-2.png';
import qlStep3Img from '../img/ql-step-3.png';
import qlStep4Img from '../img/ql-step-4.png';
import { CheckCircleFilled, ExclamationCircleFilled, SyncOutlined } from '@ant-design/icons';
import './index.css';
type Props = {
  clientStatus: string;
  exeStatus: string;
};
const Help: React.FC<Props> = ({ clientStatus, exeStatus }) => {
  const weightStyle = {
    fontWeight: '600',
    marginLeft: '10px'
  };
  const successIconStyle = {
    color: '#0AC448',
    fontSize: '42px',
    marginRight: '15px'
  };
  const infoIconStyle = {
    color: 'rgba(171, 171, 171, 0.65)',
    fontSize: '42px',
    marginRight: '15px'
  };
  const descriptionTitleStyle = {
    marginBottom: '16px'
  };
  const description1 = (
    <div>
      <div style={descriptionTitleStyle}>
        <span>登录千牛账号时需选择</span>
        <span style={weightStyle}>多账号登录</span>
      </div>
      <div className="bottom_img">
        <img src={qlStep1Img} />
        <img src={qlStep2Img} />
      </div>
    </div>
  );
  const description2 = (
    <div>
      <div style={descriptionTitleStyle}>
        <span>{'在“系统设置 > 多店铺设置 > 通用 > 无障碍”里'}</span>
        <span style={weightStyle}>启用讲述人模式</span>
      </div>
      <div className="bottom_img">
        <img src={qlStep3Img} />
      </div>
    </div>
  );
  const description3 = (
    <div>
      <div style={descriptionTitleStyle}>
        <span>{'在“系统设置 > 接待设置 > 会话窗口”里 打开气泡模式'}</span>
        <span style={weightStyle}>多账号登录</span>
      </div>
      <div className="bottom_img">
        <img src={qlStep4Img} />
      </div>
    </div>
  );
  return (
    <div className="help_container">
      {/* {
        clientStatus === '已启动' &&
      } */}
      <div className="help_title">
        {clientStatus === '已启动' && (
          <>
            <CheckCircleFilled style={successIconStyle} />
            <span>AI助手已启用</span>
          </>
        )}

        {(clientStatus === '错误' || clientStatus === '启动中') && (
          <>
            <SyncOutlined spin style={infoIconStyle} />
            <span>AI助手启用中</span>
          </>
        )}
        {(clientStatus === '已停止' || clientStatus === '未知') && (
          <>
            <ExclamationCircleFilled style={infoIconStyle} />
            <span>AI助手未启用</span>
          </>
        )}
        {exeStatus === '已启动' ? (
          <>
            <CheckCircleFilled style={successIconStyle} />
            <span>千牛已启动</span>
          </>
        ) : (
          <>
            <ExclamationCircleFilled style={infoIconStyle} />
            <span>千牛未启动</span>
          </>
        )}
      </div>
      <div className="help_subtext">请先确认您的千牛客户端是否按照以下要求登录并设置：</div>
      <div style={{ margin: '0 auto' }}>
        <Steps
          direction="vertical"
          items={[
            {
              title: '多账号登录',
              status: 'process',
              description: description1
            },
            {
              title: '启用讲述人模式',
              status: 'process',
              description: description2
            },
            {
              title: '打开气泡模式',
              status: 'process',
              description: description3
            }
          ]}
        />
      </div>
    </div>
  );
};

export default Help;

import { types } from 'mobx-state-tree';
export const UserInfo = types
  .model('UserInfo', {
    aikfPackageAsset: types.frozen(),
    permission: types.string
  })
  .actions((self) => ({
    setAikfPackageAsset(val: any) {
      self.aikfPackageAsset = val;
    },
    setPermission(val: string) {
      self.permission = val;
    }
  }))
  .actions(() => {
    // const pullAikfPackageAsset = flow(function* (username?: string) {
    //   const res = yield getAikfPackageAsset();
    //   self.setAikfPackageAsset(res.data);
    //   self.setPermission(res.data?.permission ?? "workspace");
    //   if (username) {
    //     const accountData = JSON.parse(
    //       localStorage.getItem("accountData") ?? "[]",
    //     );
    //     localStorage.setItem(
    //       "accountData",
    //       JSON.stringify([
    //         ...accountData.map((item) => {
    //           if (item.username === username) {
    //             return {
    //               username,
    //               password: item.password,
    //               assetName: res.data.name,
    //               current: true,
    //             };
    //           }
    //           return {
    //             ...item,
    //             current: false,
    //           };
    //         }),
    //       ]),
    //     );
    //   }
    //   return res;
    // });

    return {
      // pullAikfPackageAsset,
    };
  });

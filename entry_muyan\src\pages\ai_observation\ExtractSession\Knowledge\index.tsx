/* eslint-disable react/display-name */
import { observer } from 'mobx-react-lite';
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import icon_add from '@/assets/icons/icon_addy.svg';
import { KonwledgeList } from './KonwledgeList';
import { store } from '@/store';
import { Spin, AutoComplete, message } from 'antd';
import { getThread } from '@/api/myqaApi';
type Props = {
  obj;
  kb_name;
  currentThread;
};
const Knowledge = observer(
  forwardRef(({ obj, kb_name, currentThread }: Props, ref) => {
    const [search, setSearch] = useState('');
    const [intention, setIntention] = useState('');
    const { knowledge, extractSession } = store;
    const [loading, setLoading] = useState(true);
    const [options, setOptions] = useState<any[]>([]);
    useEffect(() => {
      init();
    }, []);
    function init() {
      setLoading(true);
      knowledge
        .migrateDocs({
          file_id: obj.id,
          kb_name: kb_name,
          override: true,
          assistant_id: localStorage.getItem('assistantId')
        })
        .then((res) => {
          const version_id = res.data.version_info[0]?.version_id ?? '';
          knowledge.setVersionId(version_id);
          knowledge
            .loadDomain(kb_name, version_id)
            .then((kres) => {
              knowledge.setList(kres.data);
              getThread(currentThread.id ?? '').then((tres: any) => {
                const threadIds = tres?.metadata?.child ?? [];
                knowledge.getDefaultIntentIds([currentThread.id, ...threadIds]).then((dres) => {
                  const de_weight_ids = Array.from(new Set(dres.intent_ids));
                  const extract = tres?.metadata?.extract ?? false;
                  if (!extract) {
                    extractSession.setIntentionList(
                      Array.from(
                        new Set([
                          ...(currentThread?.origin?.metadata_?.intent_ids ?? []),
                          ...de_weight_ids
                        ])
                      )
                    );
                  }
                });
              });
            })
            .finally(() => {
              setLoading(false);
              setOptions(knowledge.list);
              extractSession.setIntentionList(currentThread?.origin?.metadata_?.intent_ids ?? []);
              extractSession.setEditList(currentThread?.origin?.metadata_?.editList ?? []);
            });
        });
    }
    useImperativeHandle(ref, () => {
      return {
        init
      };
    });
    const onSelect = (value: string, options) => {
      setSearch(value);
      setIntention(options.intent_id);
      extractSession.setCurrentIntentionId(options.intent_id);
    };

    const getPanelValue = (searchText: string) => {
      return knowledge.list.filter((item) => {
        return (
          item.terms.toLowerCase().includes(searchText.toLowerCase()) ||
          isQuery(item.query, searchText) ||
          isResponse(item.response, searchText)
        );
      });
    };

    const box: any = document.getElementById('extract_session_popup_container');
    const isQuery = (query: any[], val: string) => {
      for (let i = 0; i < query.length; i++) {
        if (query[i].query.toLowerCase().includes(val.toLowerCase())) return true;
      }
      return false;
    };
    const isResponse = (response: any[], val: string) => {
      for (let i = 0; i < response.length; i++) {
        if (response[i].response.toLowerCase().includes(val.toLowerCase())) return true;
      }
      return false;
    };
    return (
      <div
        style={{
          flex: 1,
          marginLeft: '20px'
        }}
      >
        <div
          style={{
            display: 'flex',
            paddingLeft: '28px',
            alignItems: 'center'
          }}
        >
          <AutoComplete
            options={options}
            style={{ width: 300 }}
            onSelect={onSelect}
            onSearch={(text) => {
              setOptions(getPanelValue(text));
              setSearch(text);
              const arr = knowledge.list.filter((item) => item.terms === text);
              if (arr.length > 0) {
                setIntention(arr[0]?.intent_id);
                extractSession.setCurrentIntentionId(arr[0]?.intent_id);
              } else setIntention('');
            }}
            getPopupContainer={() => box}
            fieldNames={{
              label: 'terms',
              value: 'terms'
            }}
          />
          <div id="extract_session_popup_container" className=""></div>
          <img
            src={icon_add}
            alt="icon"
            style={{
              height: '16px',
              width: '16px',
              marginLeft: '10px',
              cursor: 'pointer'
            }}
            onClick={() => {
              if (intention) {
                if (extractSession.intentionList.includes(intention)) {
                  message.warning('该意图已存在与列表中!');
                } else {
                  extractSession.setIntentionList([...extractSession.intentionList, intention]);
                  extractSession.setCurrenQuerytList(
                    knowledge.list
                      .filter((item) => item.intent_id === intention)
                      .map((item) => item.query)
                      .flat()
                      .map((item) => item.query)
                  );
                }
              } else {
                knowledge
                  .addProductQa({
                    product_id: '',
                    terms: search,
                    answer_rule: '',
                    cate_kb_id: '',
                    cate_id: '',
                    kb_name: kb_name
                  })
                  .then((res) => {
                    setSearch('');
                    extractSession.setSearch('');
                    knowledge.loadDomain(kb_name, knowledge.versionId).then((res) => {
                      knowledge.setList(res.data);
                    });
                    knowledge.setModifier(knowledge.modifier + 1);
                    const id = res.data.intent_id.replace(/-/g, '');
                    extractSession.setNewIntention([...extractSession.newIntention, id]);
                    extractSession.setIntentionList([...extractSession.intentionList, id]);
                  });
              }
            }}
          />
        </div>
        <Spin spinning={loading}>
          <KonwledgeList search={search} kb_name={kb_name} />
        </Spin>
      </div>
    );
  })
);
Knowledge.displayName = 'Knowledge';

export { Knowledge };

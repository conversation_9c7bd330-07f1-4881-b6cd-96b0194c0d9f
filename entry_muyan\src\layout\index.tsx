import React from 'react';
import { Layout } from 'antd';
import HerderView from './Herder';
import { Outlet, useLocation } from 'react-router-dom';
import Menu from './Menu';
import { observer } from 'mobx-react-lite';
const { Header, Sider, Content } = Layout;

const headerStyle: React.CSSProperties = {
  height: '56px',
  paddingInline: '24px',
  width: '100%',
  background: '#fff',
  borderBottom: '1px solid #E5E5E5'
};

const contentStyle: React.CSSProperties = {
  backgroundColor: '#F2F3F5',
  overflow: 'auto',
  height: 'calc(100vh - 58px)'
};

const siderStyle: React.CSSProperties = {
  overflow: 'auto',
  height: 'calc(100vh - 58px)',
  width: '88px',
  backgroundColor: '#fff',
  borderRight: '1px solid #E5E5E5',
  position: 'relative'
};

const layoutStyle = {
  overflow: 'hidden',
  width: '100vw',
  height: '100vh'
};

const LayoutBox: React.FC = observer(() => {
  const location = useLocation();
  return (
    <Layout style={layoutStyle}>
      <Header style={headerStyle}>
        <HerderView />
      </Header>
      <Layout>
        {!['/setmeal'].includes(location.pathname) && (
          <Sider width="88px" style={siderStyle}>
            <Menu />
          </Sider>
        )}
        <Content style={contentStyle}>
          <Outlet key={Date.now()} />
        </Content>
      </Layout>
    </Layout>
  );
});

export default LayoutBox;

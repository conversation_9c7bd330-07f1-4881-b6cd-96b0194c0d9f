/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useRef, useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import style from './index.module.css';
import './input.less';
import icon_copy from '@/assets/icons/icon_copy.svg';
import icon_log from '@/assets/icons/icon_log.svg';
import icon_log_ac from '@/assets/icons/icon_log_ac.svg';
import icon_dui from '@/assets/icons/icon_dui.svg';
import copy from 'copy-to-clipboard';
import { timeformat } from './timeformat';
import { Spin, Input, Empty, Tooltip } from 'antd';
import AiIcon from '@/assets/icons/ai_customer.svg';
import default_content from '@/assets/imgs/default_content.png';
import { messagesCreate, platformTask, getMessagesItem } from '@/api/myqaApi';
import { v4 as uuidv4 } from 'uuid';
const Item: React.FC<{
  setShowLog: (showLog: boolean, id: string) => void;
  data: any;
  active: boolean;
  name: string;
  beSilent: boolean;
}> = ({ setShowLog, data, active, name, beSilent }) => {
  const [isCopy, setIsCopy] = useState(true);
  const [copyId, setCopyId] = useState(null);
  const onCopy = (message: string, id: any): void => {
    if (!isCopy) return;
    copy(message);
    setIsCopy(false);
    setCopyId(id);
    setTimeout(() => {
      setCopyId(null);
      setIsCopy(true);
    }, 3000);
  };

  const [contrastData, setContrastData] = useState<any>({});
  useEffect(() => {
    if (data.metadata?.child?.[0]) {
      getMessagesItem(data.metadata.child[0]).then((res) => {
        setContrastData(res);
      });
    }
  }, []);

  return (
    <div
      className={style.item}
      style={{
        marginBottom:
          beSilent &&
          data.role !== 'user' &&
          !contrastData?.metadata?.chat_log?.meta_data?.merge_list.length
            ? 0
            : ''
      }}
    >
      {beSilent && data.role === 'assistant' ? (
        <></>
      ) : (
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: data.role === 'user' ? 'left' : 'right'
          }}
        >
          <div className={style.header}>
            {data.role === 'assistant' && <img src={AiIcon} style={{ marginRight: 5 }} />}
            <div className={style.name}>
              {data.role === 'user'
                ? (data.metadata?.uid ?? '用户')
                : data.role === 'manual'
                  ? '人工回复'
                  : name || '小可'}
            </div>
            <div className={style.time} id={data.updated_at ?? data.created_at}>
              {timeformat(data.updated_at ?? data.created_at)}
            </div>
          </div>
          <div
            className={`${style.message} ${data.role === 'user' ? '' : data.role === 'assistant' ? style.ai_message : style.manual_message}`}
          >
            <div
              style={{
                wordBreak: 'break-all',
                whiteSpace: 'pre-wrap'
              }}
            >
              {data.content.map((item) => {
                if (item.type !== 'picture') {
                  return <div key={uuidv4()}>{item.text[0].value}</div>;
                }
                if (item.type === 'picture') {
                  return (
                    <img
                      style={{
                        width: '200px',
                        height: 'auto'
                      }}
                      src={item.text[0].value}
                      alt="img"
                      key={uuidv4()}
                    />
                  );
                }
              })}
            </div>
            <div className={style.bottom_icon}>
              <Tooltip title={data.origin_message !== copyId ? '复制' : '已复制'}>
                <img
                  className={style.icon_button}
                  src={data.origin_message !== copyId ? icon_copy : icon_dui}
                  onClick={() => {
                    onCopy(data.content[0].text[0].value, data.origin_message);
                  }}
                  alt="icon_log"
                />
              </Tooltip>
              {data.role !== 'user' && data.role !== 'manual' && (
                <Tooltip title="日志">
                  <img
                    className={style.icon_button}
                    onClick={() => {
                      setShowLog(true, data.id);
                    }}
                    src={active ? icon_log_ac : icon_log}
                    alt="icon_log"
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      )}
      {beSilent && data.role !== 'user' && (
        <div style={{ width: '100%' }}>
          {contrastData?.metadata?.chat_log?.meta_data?.merge_list?.map((item, index) => {
            return (
              <div
                key={item.user_id}
                style={{
                  marginBottom:
                    index !== contrastData?.metadata?.chat_log?.meta_data?.merge_list?.length - 1
                      ? '58px'
                      : 0,
                  position: 'relative',
                  width: '100%',
                  display: 'flex',
                  justifyContent: 'right'
                }}
              >
                <div className={style.headerRight}>
                  <div className={style.time} style={{ marginRight: 8 }}>
                    {timeformat(item.updated_at ?? item.created_at)}
                  </div>
                  <div className={style.name} style={{ marginRight: 0 }}>
                    {item.user_id}
                  </div>
                </div>
                <div className={`${style.message} ${style.manual_message}`}>
                  <div
                    style={{
                      wordBreak: 'break-all',
                      whiteSpace: 'pre-wrap'
                    }}
                  >
                    {item.content}
                  </div>
                  <div className={style.bottom_icon}>
                    <img
                      className={style.icon_button}
                      src={copyId !== item.assistant_id + index ? icon_copy : icon_dui}
                      onClick={() => {
                        onCopy(item.content, item.assistant_id + index);
                      }}
                      alt="icon_log"
                    />
                  </div>
                </div>
              </div>
            );
          })}
          {/* 适配老数据 */}
          {data.metadata?.manual_message &&
            !contrastData?.metadata?.chat_log?.meta_data?.merge_list && (
              <div style={{ marginBottom: '58px' }}>
                <div className={style.header}>
                  <div className={style.name}>人工回复</div>
                  <div className={style.time}>{timeformat(data.updated_at ?? data.created_at)}</div>
                </div>
                <div className={style.message}>
                  <div
                    style={{
                      wordBreak: 'break-all',
                      whiteSpace: 'pre-wrap'
                    }}
                  >
                    {data.metadata.manual_message}
                  </div>
                  <div className={style.bottom_icon}>
                    <img
                      className={style.icon_button}
                      src={isCopy ? icon_copy : icon_dui}
                      onClick={() => {
                        onCopy(data.metadata.manual_message, null);
                      }}
                      alt="icon_log"
                    />
                  </div>
                </div>
              </div>
            )}
        </div>
      )}
    </div>
  );
};
type Props = {
  setShowLog: (showLog: boolean, id: string) => void;
  onChange: (id: string) => any;
  data: any[];
  loading: boolean;
  name: string;
  transfer: any;
  beSilent: boolean;
  isManual?: boolean;
  startTime: any;
};

const Chat = forwardRef(
  (
    { setShowLog, data, loading, name, transfer, onChange, beSilent, isManual, startTime }: Props,
    ref
  ) => {
    const [currentId, setCurrentId] = useState('');
    const [value, setValue] = useState('');
    const [lock, setLock] = useState(false);
    const scrollAreaRef = useRef<HTMLInputElement | null>(null);
    const scrollToBottom = () => {
      const id = data.find((item) => {
        return (item.updated_at ?? item.created_at) > startTime && item.role === 'user';
      })?.updated_at;
      if (scrollAreaRef.current) {
        const scrollHeight = document.getElementById(id)?.getBoundingClientRect()?.top;
        scrollAreaRef.current.scrollTo({
          top: scrollHeight ? scrollHeight - 70 : scrollAreaRef.current.scrollHeight,
          behavior: 'smooth'
        });
      }
    };
    useImperativeHandle(ref, () => {
      return {
        scrollToBottom
      };
    });
    const createMsg = (content) => {
      setLock(false);
      if (lock || !content) return;
      setLock(true);
      messagesCreate({
        content,
        thread_id: transfer.conversation_id,
        assistant_name: name
      })
        .then(() => {
          platformTask({
            task_name: 'create',
            mall_id: transfer.mall_id,
            mall_name: transfer.mall_name,
            customer_id: transfer.customer_id,
            meta_data: {
              conversation_id: transfer.conversation_id
            }
          });
          setValue('');
          onChange(transfer.conversation_id)?.then(() => {
            setTimeout(() => {
              scrollToBottom();
            }, 10);
          });
        })
        .finally(() => {
          setLock(false);
        });
    };
    return (
      <div
        style={{
          flex: '1',
          height: '100%',
          backgroundColor: '#fff',
          position: 'relative'
        }}
      >
        <div
          ref={scrollAreaRef}
          className={`${style.chat}  ${transfer?.status === 'failed' ? style.transfer : ''}`}
        >
          {loading && (
            <Spin tip="Loading..." spinning={loading} style={{ flex: 1 }}>
              <div style={{ height: '60vh', width: '100%' }}></div>
            </Spin>
          )}

          {data?.length > 0 || loading ? (
            data.map((item) => {
              return (
                <div
                  key={item.id}
                  onClick={() => {
                    setCurrentId(item.id);
                  }}
                >
                  <Item
                    beSilent={beSilent}
                    name={name}
                    data={item}
                    active={currentId === item.id}
                    setShowLog={setShowLog}
                  />
                </div>
              );
            })
          ) : (
            <div style={{ marginTop: 'calc(50% - 200px)' }}>
              <Empty
                image={default_content}
                imageStyle={{
                  height: 120
                }}
                description={
                  <span
                    style={{
                      fontSize: '12px',
                      color: '#666666',
                      lineHeight: '18px'
                    }}
                  >
                    暂无内容
                  </span>
                }
              />
            </div>
          )}
        </div>
        {/* {transfer?.status === "failed" && (
          <div
            style={{
              display: "flex",
              padding: "0px 24px",
            }}
          > */}
        {isManual && (
          <Input
            value={lock ? '发送中...' : value}
            onChange={(e: any) => {
              setValue(e.target.value);
            }}
            disabled={transfer?.status === 'failed' ? false : true}
            className="send_input"
            onPressEnter={() => createMsg(value)}
            addonAfter={
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                version="1.1"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                className="sendIcon"
                onClick={() => {
                  if (transfer?.status === 'failed') {
                    createMsg(value);
                  }
                }}
              >
                <g>
                  <g>
                    <path
                      d="M4.965587421875,1.7602229999999999C4.438257421875,1.4786916,3.825457421875,1.4246494,3.258977421875,1.602723C2.035124421875,1.964948,1.343502421875,3.25458,1.709887421875,4.46371L3.461017421875,10.26116C3.621667421875,10.79299,4.111667421875,11.15684,4.667217421875,11.15684L10.873137421875,11.15684C11.341747421875,11.16132,11.710457421875,11.5347,11.713257421875,11.9968C11.702857421875,12.466,11.329617421875,12.8348,10.873137421875,12.8367L4.6626474218750005,12.8367C4.1092774218749994,12.8367,3.620677421875,13.1977,3.458207421875,13.7267L1.683628421875,19.5035C1.505604421875,20.0768,1.557751821875,20.6913,1.814919421875,21.2359C2.399121421875,22.3634,3.788217421875,22.8224,4.939347421875,22.2595L21.165357421875,14.0703C21.594557421875,13.8552,21.952757421875,13.4977,22.163057421875,13.0466C22.752457421875,11.9182,22.296957421875,10.52744,21.165357421875,9.94943L4.965587421875,1.7602229999999999Z"
                      fill={
                        transfer?.status === 'failed' && !lock && value ? '#2E74FF' : 'currentColor'
                      }
                    />
                  </g>
                </g>
              </svg>
              // <img
              //   src={SendIcon}
              //   onClick={() => {
              //     createMsg(value);
              //   }}
              // />
            }
          />
        )}
        {/* <Button
              style={{
                padding: "0px 16px",
                width: "80px",
                height: "32px",
                lineHeight: "32px",
                marginLeft: "10px",
              }}
              type="primary"
            >
              发送
            </Button> */}
        {/* </div>
        )} */}
      </div>
    );
  }
);
Chat.displayName = 'Chat';
export default Chat;

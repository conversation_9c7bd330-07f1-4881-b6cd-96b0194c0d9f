import Modal from '@/components/Modal';
import default_ceiling from '@/assets/imgs/default_ceiling.png';
import Button from '@/components/Button';
type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
};
const UpperLimithint: React.FC<Props> = ({ open, setOpen }: Props) => {
  return (
    <Modal open={open} title="温馨提示" footer={false} setOpen={setOpen}>
      <div
        style={{
          textAlign: 'center',
          marginTop: '44px'
        }}
      >
        <img
          src={default_ceiling}
          style={{
            width: '120px',
            height: '120px'
          }}
          alt="default_ceiling"
        />
        <div
          style={{
            fontWeight: 400,
            fontSize: '12px',
            color: '#666666',
            lineHeight: '18px',
            fontStyle: 'normal',
            textTransform: 'none',
            marginTop: '20px'
          }}
        >
          抱歉，添加账户已达上限~
        </div>
        <Button
          style={{
            width: '128px',
            height: '36px',
            lineHeight: '36px',
            letterSpacing: '2px',
            marginTop: '24px',
            marginBottom: '28px'
          }}
          type="primary"
        >
          去升级
        </Button>
      </div>
    </Modal>
  );
};

export default UpperLimithint;

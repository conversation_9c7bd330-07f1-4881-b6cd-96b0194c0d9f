/* eslint-disable @typescript-eslint/no-explicit-any */
import { useMemo } from 'react';
interface SvgIconProps {
  prefix?: string;
  name: string;
  color?: string;
  size?: number | string;
  className?: string;
  onClick?: (e) => void;
}

const SvgIcon = (props: SvgIconProps): React.ReactElement => {
  const {
    prefix = 'icon',
    name,
    color = '#000000',
    size = 16,
    className = '',
    onClick = (e: any): void => {
      e.preventDefault();
    }
  } = props;
  const symbolId = useMemo(() => `#${prefix}-${name}`, [prefix, name]);
  return (
    <svg
      aria-hidden="true"
      className={className}
      width={size}
      height={size}
      fill={color}
      onClick={onClick}
    >
      <use href={symbolId} fill={color} />
    </svg>
  );
};
export default SvgIcon;

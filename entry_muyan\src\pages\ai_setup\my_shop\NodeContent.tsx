/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import ReactJson from 'react-json-view';
import Modal from '@/components/Modal';
export const NodeContent: React.FC<{
  jsonData: any;
  title: string;
  open: boolean;
  setOpen: (bool: boolean) => void;
}> = ({ jsonData, title, open, setOpen }) => {
  return (
    <Modal open={open} footer={false} setOpen={setOpen} title={title}>
      <div
        style={{
          overflowY: 'auto',
          height: 'calc(100vh - 300px)',
          padding: '10px 20px'
        }}
      >
        <ReactJson src={jsonData} />
      </div>
    </Modal>
  );
};

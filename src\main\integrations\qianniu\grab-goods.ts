import got from '../../utils/got';
import BurrowAdapter, { GoodsList } from './burrow-adapter';
import _ from 'lodash';
import ms from 'ms';
import delay, { rangeDelay } from 'delay';
import { db } from '../../utils/sqlite';
import dayjs from 'dayjs';
import { createPromiseCapability } from '../../utils/promise';

export default class GrabGoods {
  #burrowAdapter: BurrowAdapter;
  #cs: string[];
  #goods: Record<string, GoodsList['itemList']> = {};
  #batchSize = 100;

  #getCsPromise: ReturnType<typeof createPromiseCapability>;

  #isClosed: boolean;

  constructor(private ctx) {
    this.#burrowAdapter = new BurrowAdapter('http://127.0.0.1:22233');
    // this.#burrowAdapter = new BurrowAdapter('http://192.168.0.47:22233');
    // this.#getCsPromise = createPromiseCapability();
    this.#isClosed = false;
    // this.start();
  }

  async start(fromBackend = false, cs?: [string]) {
    this.ctx.log.info('开始抓取商品', fromBackend, cs);
    await this.getCs(fromBackend, cs);
    await this.getGoods();
  }

  async getCs(fromBackend: boolean, cs?: [string]) {
    try {
      this.#getCsPromise = createPromiseCapability();
      if (this.#isClosed) {
        this.#getCsPromise.reject(new Error('已停止1'));
        return;
      }
      this.ctx.log.debug('开始获取客服列表');
      const res = await this.#burrowAdapter.getCs();
      if (res.length === 0) {
        throw new Error('没有可抓取的客服');
      }
      const whiteList = fromBackend ? cs : this.ctx.agentConfig.grabGoodsWhiteList || [];
      this.#cs = res.filter((cs) => whiteList.some((white) => cs.includes(white)));
      this.ctx.log.info('需要抓取的客服列表', this.#cs);
      if (fromBackend) {
        this.#getCsPromise.resolve();
      } else {
        db.all(
          "SELECT shop FROM grab_goods WHERE DATE(last_grab_time) = DATE('now', 'localtime')",
          (err, rows: { shop: string }[]) => {
            if (err) {
              this.ctx.log.error('查询抓取商品记录失败', err);
              return;
            }
            this.ctx.log.info('查询抓取商品记录成功', rows);
            this.#cs = this.#cs.filter((cs) => rows.every((item) => !cs.includes(item.shop)));
            this.ctx.log.info('过滤后的客服列表', this.#cs);
            this.#getCsPromise.resolve();
          }
        );
      }
    } catch (error) {
      this.ctx.log.error('获取客服列表失败', error);
      setTimeout(this.getCs.bind(this), ms('5s'));
    }
    return this.#getCsPromise.promise;
  }

  async getGoods() {
    for (const cs of this.#cs) {
      this.#goods[cs] = [];
      let page = 1;

      // eslint-disable-next-line
      while (true) {
        try {
          if (this.#isClosed) {
            throw new Error('已停止2');
          }
          this.ctx.log.debug('开始获取商品列表', cs, 'page', page);
          const res = await this.#burrowAdapter.getGoodsList(cs, page++);
          // await rangeDelay(303, 856);
          if (!res) {
            break;
          }
          this.#goods[cs].push(...res);
        } catch (error) {
          this.ctx.log.error('获取商品列表失败', error);
          return;
        }
      }
      page = 1;
      // eslint-disable-next-line
      while (true) {
        try {
          if (this.#isClosed) {
            throw new Error('已停止5');
          }
          this.ctx.log.debug(
            '开始获取待上架商品列表',
            cs,
            this.ctx.agentConfig.shopType,
            'page',
            page
          );
          const res = await this.#burrowAdapter.getTimingSaleGoods(
            cs,
            this.ctx.agentConfig.shopType,
            page++
          );
          // await rangeDelay(303, 856);
          if (!res.length) {
            break;
          }
          this.#goods[cs].push(...res);
        } catch (error) {
          this.ctx.log.error('获取待上架商品列表失败', error);
          break;
        }
      }
      this.ctx.log.debug('开始获取商品详情', cs, '商品总数', this.#goods[cs].length);
      try {
        await this.getGoodsDetail(cs);
      } catch (error) {
        this.ctx.log.error('获取商品详情失败', error);
      }
    }
  }

  async getGoodsDetail(cs) {
    // get user
    const target = await this.#burrowAdapter.getUser(cs);
    for (let i = 0; i < this.#goods[cs].length; i++) {
      if (this.#isClosed) {
        throw new Error('已停止3');
      }
      this.ctx.log.debug('开始获取商品详情', i, 'itemId', this.#goods[cs][i].itemId);
      const res = await this.#burrowAdapter.getGoodsDetail(cs, target, this.#goods[cs][i].itemId);
      _.assign(this.#goods[cs][i], res);
      try {
        const res = await this.#burrowAdapter.getGoodsDesc(cs, target, this.#goods[cs][i].itemId);
        _.assign(this.#goods[cs][i], { desc: res });
      } catch (e) {
        this.ctx.log.debug('开始获取商品desc失败', i, 'itemId', this.#goods[cs][i].itemId, e);
      }

      if ((i + 1) % this.#batchSize === 0 || i === this.#goods[cs].length - 1) {
        const start = Math.floor(i / this.#batchSize) * this.#batchSize;
        this.ctx.log.debug('开始上报商品', start, i);
        let success = false;

        while (!success) {
          if (this.#isClosed) {
            throw new Error('已停止4');
          }
          try {
            await this.uploadGoodsInfo(this.#goods[cs].slice(start, i + 1), cs.split(':')[0]);
            success = true;
          } catch (error) {
            this.ctx.log.error('上报商品失败', error);
            await delay(10000);
          }
        }
      }

      await rangeDelay(758, 1340);
    }
  }

  // 每次上报100个商品？5页
  async uploadGoodsInfo(goodsList, shop) {
    const path = `/message/api/product/autoUpdateProduct`;

    const res = await got
      .post(this.ctx.env.host + path, {
        json: {
          mallId: shop,
          products: goodsList
        },
        headers: {
          authorization: this.ctx.env.accessToken
        }
      })
      .json();

    this.ctx.log.info('上传商品信息成功', res);
    db.run(
      `INSERT OR REPLACE INTO grab_goods (shop, last_grab_time) VALUES (?, ?)`,
      [shop, dayjs().format('YYYY-MM-DD HH:mm:ss')],
      (err) => {
        if (err) {
          this.ctx.log.error('更新抓取商品记录失败', err);
        } else {
          this.ctx.log.info('更新抓取商品记录成功');
        }
      }
    );
  }

  close() {
    this.#isClosed = true;
  }
}

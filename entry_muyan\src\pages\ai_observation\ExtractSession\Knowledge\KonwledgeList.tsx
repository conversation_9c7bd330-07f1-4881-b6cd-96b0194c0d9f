import { observer } from 'mobx-react-lite';
import { KnowledgeItem } from './KnowledgeItem';
import { store } from '@/store';
import VirtualList from 'rc-virtual-list';
import { List } from 'antd';
type Props = { search: string; kb_name: string };
export const KonwledgeList = observer(({ search, kb_name }: Props) => {
  const { knowledge, extractSession } = store;
  const getList = (intentionList) => {
    return knowledge.list.filter((item) => {
      return intentionList.includes(item.intent_id);
    });
  };
  return (
    <div
      style={{
        flex: 1,
        height: '500px'
      }}
    >
      <div
        style={{
          paddingLeft: '28px',
          paddingRight: '16px',
          display: 'flex',
          fontSize: '12px',
          marginTop: '8px',
          justifyContent: 'space-between'
        }}
      >
        <div
          style={{
            flex: 1,
            marginRight: '8px'
          }}
        >
          意图
        </div>
        <div
          style={{
            flex: 1,
            marginRight: '8px'
          }}
        >
          回复
        </div>
        <div
          style={{
            display: 'flex',
            flex: 1,
            marginRight: '8px',
            justifyContent: 'space-between'
          }}
        >
          <div>问法</div>
        </div>
      </div>
      {knowledge.list.length > 0 && (
        <List>
          <VirtualList
            data={getList(extractSession.intentionList)}
            fullHeight={true}
            height={477}
            itemHeight={100}
            virtual={true}
            itemKey="id"
          >
            {(item: any) => (
              <div
                key={item.id}
                onClick={() => {
                  extractSession.setCurrentIntentionId(item.intent_id);
                  extractSession.setCurrenQuerytList(
                    knowledge.list
                      .filter((fitem) => fitem.intent_id === item.intent_id)
                      .map((item) => item.query)
                      .flat()
                      .map((item) => item.query)
                  );
                }}
              >
                <KnowledgeItem item={item} search={search} kb_name={kb_name} />
              </div>
            )}
          </VirtualList>
        </List>
      )}
    </div>
  );
});

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';
import { Table } from 'antd';
import type { TableColumnsType } from 'antd';
import Button from '@/components/Button';
import * as XLSX from 'xlsx';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';

interface DataType {
  key: React.Key;
  name: string;
  age: number;
  address: string;
  dataSource: DataType[];
}
type Props = {
  data: any[];
  time: any[];
  isDay: boolean;
};

const TableBox: React.FC<Props> = ({ data, time, isDay: isOneDay }: Props) => {
  const columns: TableColumnsType<DataType> = [
    {
      title: isOneDay ? '小时' : '日期',
      dataIndex: isOneDay ? 'hour' : 'bizdate'
    },
    { title: '总接待数', dataIndex: 'user_cnt' },
    {
      title: '独立接待数',
      dataIndex: '',
      render: (r) => {
        return <>{r.user_cnt - r.change_human_user_cnt}</>;
      }
    },
    { title: '转人工数', dataIndex: 'change_human_user_cnt' },
    {
      title: '独立接待率',
      dataIndex: '',
      render: (r) => {
        return (
          <>
            {((((r.user_cnt - r.change_human_user_cnt) / (r.user_cnt || 1)) * 100).toFixed(2) ||
              '-') + '%'}
          </>
        );
      }
    }
    // { title: "下单数", dataIndex: "pingtai" },
    // { title: "直接下单率", dataIndex: "pingtai" },
    // { title: "转人工下单率", dataIndex: "pingtai" },
  ];

  useEffect(() => {
    setHeight(getWindowHeight());
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const [height, setHeight] = useState<number>(0);
  const getWindowHeight = (): number => {
    return (
      (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) -
      490
    );
  };
  const handleResize = (): void => {
    setHeight(getWindowHeight());
  };

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(
      data.map((item) => {
        return {
          日期: isOneDay ? item.hour : item.bizdate,
          总接待数: item.user_cnt,
          独立接待数: item.user_cnt - item.change_human_user_cnt,
          转人工数: item.change_human_user_cnt,
          独立接待率:
            ((((item.user_cnt - item.change_human_user_cnt) / (item.user_cnt || 1)) * 100).toFixed(
              2
            ) || '-') + '%'
        };
      })
    );
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });
    const blob = new Blob([excelBuffer], {
      type: 'application/octet-stream'
    });
    const isDay = dayjs(time[0]) === dayjs(time[1]);
    saveAs(
      blob,
      `${isDay ? dayjs(time[0]).format('YYYY-MM-DD') : dayjs(time[0]).format('YYYY-MM-DD') + '-' + dayjs(time[1]).format('YYYY-MM-DD')}.xlsx`
    );
  };

  return (
    <div>
      <div
        style={{
          textAlign: 'right',
          marginBottom: '16px'
        }}
      >
        <Button
          style={{
            width: '70px',
            height: '36px',
            lineHeight: '36px'
          }}
          onClick={() => {
            exportToExcel();
          }}
        >
          导出
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        bordered
        scroll={{ y: height, scrollToFirstRowOnChange: true }}
      />
    </div>
  );
};

export default TableBox;

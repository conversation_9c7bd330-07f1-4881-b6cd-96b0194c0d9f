import { createContext, useContext, ReactNode } from 'react';

const IsDevContext = createContext<boolean>(false);
// const IsDevContextProvider = IsDevContext.Provider;
const useIsDev = () => useContext(IsDevContext);
const IsDevProvider = ({ children }: { children: ReactNode }) => {
  const isDev = ['localhost' /*'client.test.dongchacat.cn'*/].includes(location.hostname);

  return <IsDevContext.Provider value={isDev}>{children}</IsDevContext.Provider>;
};

export { IsDevProvider, useIsDev };

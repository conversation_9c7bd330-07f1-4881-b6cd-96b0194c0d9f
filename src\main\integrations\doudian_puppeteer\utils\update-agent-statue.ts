export const updateAgentStatue = async (page, state) => {
  await page!.evaluate((state) => {
    (window as any).shouldClick = state;
    (window as any).agentStatue = state;
    const btn: any = document.querySelector('#pause-agent-btn');
    // 如果按钮不存在，则不执行任何操作
    if (!btn) return;
    let backgroundColor = '';
    let content = '';

    // 根据不同的状态设置按钮的背景颜色和文本内容
    switch (state) {
      case 'pause':
        backgroundColor = '#ff4343';
        content = '暂停中，点击运行';
        break;
      case 'running':
        backgroundColor = '#4CAF50';
        content = '运行中，点击暂停';
        break;
      case 'pending':
        backgroundColor = '#ff9800';
        content = '恢复中...';
        break;
    }

    // 更新按钮的样式和内容
    btn.style.backgroundColor = backgroundColor;
    btn.innerText = content;
  }, state);
};

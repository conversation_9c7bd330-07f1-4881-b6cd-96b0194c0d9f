/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import style from './index.module.css';
import icon_classification from '@/assets/icons/icon_classification.svg';
import icon_analysis from '@/assets/icons/icon_analysis.svg';
import icon_process from '@/assets/icons/icon_process.svg';
import icon_tip from '@/assets/icons/icon_tip.svg';
import icon_understand from '@/assets/icons/icon_understand.svg';
const LogItem: React.FC<{
  src: string;
  title: string;
  children: React.ReactNode;
}> = ({ src, title, children }) => {
  return (
    <div style={{ marginTop: '12px' }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <img className={style.icon} src={src} alt="icon" />
        <div className={style.title}>{title}</div>
      </div>
      <div style={{ marginTop: '8px', paddingLeft: '18px' }}>{children}</div>
    </div>
  );
};

type Props = {
  metadata: any;
  currentAssistant: any;
};
const Analyze: React.FC<Props> = ({ metadata, currentAssistant }) => {
  const answerType = new Map()
    .set('rag', '知识学习')
    .set('original', '原话回复')
    .set('reject', '失败')
    .set('reason_fail', '推理失败')
    .set('cls', '预设消息回复');
  // formatMsg函数用于处理并格式化消息内容
  function formatMsg(msg?: string): string | undefined {
    if (msg) {
      return msg.split('###')?.join(' ');
    } else {
      return undefined;
    }
  }

  // getAssistantMetaData函数用于根据给定的标签获取预设配置的元数据
  function getAssistantMetaData(cls_label?: string): string | undefined {
    const metadata = currentAssistant?.metadata?.preset_configs?.preset_label_resp;
    if (metadata && cls_label) {
      return metadata[cls_label]?.prompt ?? '-';
    } else {
      return undefined;
    }
  }

  function matches(arr: string[], selected: string[]): React.ReactNode {
    const data = arr?.flatMap((str) => {
      const bracketedStrings = str.match(/【[A-Za-z]+】/g) ?? [];
      return bracketedStrings?.map((bracketed) => ({
        key: bracketed.slice(1, -1),
        value: str
      }));
    });
    return (
      <div>
        {data.map((item) => (
          <div key={item.key} className={selected.includes(item.key) ? 'bg-[#FFFACD]' : ''}>
            {item.value}
          </div>
        ))}
      </div>
    );
  }
  return (
    <>
      <LogItem src={icon_understand} title="问题理解">
        <div>{metadata?.answer_meta?.iur ?? '-'}</div>
      </LogItem>
      <LogItem src={icon_classification} title="问题分类">
        <div>
          {answerType.get(metadata?.answer_meta?.answer_type) ??
            metadata?.answer_meta?.answer_type ??
            '-'}
        </div>
      </LogItem>
      <LogItem src={icon_tip} title="回复提示">
        <div>
          {(formatMsg(metadata?.answer_meta?.msg) ||
            getAssistantMetaData(metadata?.answer_meta?.cls_label?.label)) ??
            '-'}
        </div>
      </LogItem>
      <LogItem src={icon_analysis} title="推理分析">
        <div>
          {
            // 候选知识与推理选择以条件渲染的方式展示
            metadata?.answer_meta?.candidates?.join('') ||
            metadata?.answer_meta?.selected?.join('、') ? (
              <div>
                <div>候选知识 : </div>
                <div
                  style={{
                    borderRadius: '3px',
                    border: '1px solid #E6E6E6',
                    margin: '8px 0px',
                    padding: '8px'
                  }}
                >
                  {matches(metadata?.answer_meta?.candidates, metadata?.answer_meta?.selected)}
                </div>
                <div>推理选择 :{metadata?.answer_meta?.selected?.join('、') ?? '-'}</div>
              </div>
            ) : (
              '-'
            )
          }
        </div>
      </LogItem>
      <LogItem src={icon_process} title="推理过程">
        <div>{metadata?.answer?.reason ?? '-'}</div>
      </LogItem>
    </>
  );
};

export default Analyze;

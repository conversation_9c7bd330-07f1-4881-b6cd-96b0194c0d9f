import React from 'react';
import { Tabs, Tooltip } from 'antd';
import Favicon from './Favicon';
import parse from 'html-react-parser';

type TargetKey = React.MouseEvent | React.KeyboardEvent | string;

export type TabView = {
  id: number;
  url: string;
  title: string;
  isLoading: boolean;
  closeable: boolean;
  canGoBack?: boolean;
  canGoForward?: boolean;
  faviconUrl?: string | undefined;
};

const App: React.FC<{
  active: number;
  tabs: TabView[];
  changeTab: (data: object) => void;
  hideAdd: boolean;
  hideFavicon: boolean;
  hideAddress: boolean;
}> = ({ active, tabs, changeTab, hideAdd, hideFavicon, hideAddress }) => {
  const items = tabs.map((tab) => ({
    label: hideAddress ? (
      <div className="title">
        {hideFavicon || <Favicon url={tab.faviconUrl} isLoading={tab.isLoading} />}
        {parse(tab.title || 'New Tab')}
      </div>
    ) : (
      <Tooltip placement="bottomLeft" title={tab.title || 'New Tab'}>
        <div className="title">
          {hideFavicon || <Favicon url={tab.faviconUrl} isLoading={tab.isLoading} />}
          {parse(tab.title || 'New Tab')}
        </div>
      </Tooltip>
    ),
    key: String(tab.id),
    closable: tab.closeable
  }));

  const onChange = (newActiveKey: string) => {
    changeTab({
      action: 'active',
      payload: {
        id: +newActiveKey
      }
    });
  };

  const add = () => {
    changeTab({
      action: 'new'
    });
  };

  const remove = (targetKey: TargetKey) => {
    changeTab({
      action: 'close',
      payload: {
        id: +targetKey
      }
    });
  };

  const onEdit = (
    targetKey: React.MouseEvent | React.KeyboardEvent | string,
    action: 'add' | 'remove'
  ) => {
    if (action === 'add') {
      add();
    } else {
      remove(targetKey);
    }
  };

  // useEffect(() => {
  //   const tabContainer = document.querySelector('.x-browser-tabs');
  //   const addBtn = tabContainer?.querySelector('.ant-tabs-nav-add');
  //   const tabBtns = tabContainer?.querySelectorAll<HTMLElement>('.ant-tabs-tab .ant-tabs-tab-btn');
  //
  //   const tabContainerWidth = tabContainer?.getBoundingClientRect().width ?? 0;
  //   const addBtnWidth = addBtn?.getBoundingClientRect().width ?? 0;
  //   let tabBtnWidth = (tabContainerWidth - addBtnWidth) / tabs.length;
  //
  //   tabBtnWidth = Math.min(tabBtnWidth, 220) - 68;
  //   console.log('11111', tabContainerWidth, addBtnWidth, tabs.length, tabBtnWidth);
  //
  //   if (tabBtns) {
  //     tabBtns.forEach(t => {
  //       t.style.width = tabBtnWidth + 'px';
  //     });
  //   }
  // }, [tabs]);

  return (
    <Tabs
      // className="x-browser-tabs"
      type="editable-card"
      onChange={onChange}
      activeKey={String(active)}
      onEdit={onEdit}
      items={items}
      hideAdd={hideAdd}
    />
  );
};

export default App;

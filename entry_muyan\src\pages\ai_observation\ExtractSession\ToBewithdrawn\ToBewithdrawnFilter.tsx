import { observer } from 'mobx-react-lite';
import icon_switch from '@/assets/icons/icon_switch.svg';
import { Checkbox, Dropdown } from 'antd';
import { useState } from 'react';
type Props = {
  style?: any;
  onChange: (value: boolean, key: string) => void;
};
export const ToBewithdrawnFilter = observer(({ style = {}, onChange }: Props) => {
  const [not, setNot] = useState(true);
  const [has, setHas] = useState(true);
  const change = (value: boolean, key: string) => {
    if (key === 'not') {
      setNot(value);
    } else {
      setHas(value);
    }
    onChange(value, key);
  };

  const items = [
    {
      key: '1',
      label: (
        <div
          onClick={() => {
            change(!not, 'not');
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginLeft: '15px',
              marginRight: '15px'
            }}
          >
            <Checkbox
              value={not}
              style={{
                position: 'relative',
                top: '1px'
              }}
              onChange={() => {
                change(!not, 'not');
              }}
            />
            <div
              style={{
                fontSize: '14px',
                marginLeft: '5px',
                cursor: 'pointer'
              }}
              onClick={() => {
                change(!not, 'not');
              }}
            >
              未召回
            </div>
          </div>
        </div>
      )
    },
    {
      key: '2',
      label: (
        <div
          onClick={() => {
            change(!has, 'has');
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              marginLeft: '15px',
              marginRight: '15px'
            }}
          >
            <Checkbox
              value={has}
              style={{
                position: 'relative',
                top: '1px'
              }}
              onChange={() => {
                change(!has, 'has');
              }}
            />
            <div
              style={{
                fontSize: '14px',
                marginLeft: '5px',
                cursor: 'pointer'
              }}
              onClick={() => {
                change(!has, 'has');
              }}
            >
              已召回
            </div>
          </div>
        </div>
      )
    }
  ];
  return (
    <div style={style}>
      <Dropdown menu={{ items }} placement="bottom">
        <div>
          <img
            style={{
              width: '20px',
              height: '20px'
            }}
            src={icon_switch}
            alt="icon"
          />
        </div>
      </Dropdown>
    </div>
  );
});

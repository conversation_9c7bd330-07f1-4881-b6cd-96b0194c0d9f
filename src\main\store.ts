import Store from 'electron-store';

const schema = {
  window: {
    type: 'object',
    properties: {
      title: {
        type: 'string'
      },
      width: {
        type: 'number'
      },
      height: {
        type: 'number'
      },
      toolbarHeight: {
        type: 'number'
      },
      hideAddressBar: {
        type: 'boolean'
      },
      hideFavicon: {
        type: 'boolean'
      },
      onbeforeunload: {
        type: 'object',
        properties: {
          buttons: {
            type: 'array',
            items: {
              type: 'string'
            }
          },
          title: {
            type: 'string'
          },
          message: {
            type: 'string'
          }
        }
      }
    }
  },
  homepage: {
    type: ['string', 'object'],
    properties: {
      url: {
        type: 'string'
      },
      options: {
        type: 'object'
      }
    }
  },
  dsn: {
    type: 'string'
  }
};

const defaults = {
  window: {
    width: 1280,
    height: 768,
    toolbarHeight: 120,
    onbeforeunload: {
      buttons: ['Leave', 'Stay'],
      title: 'Do you want to leave this site?',
      message: 'Changes you made may not be saved.'
    }
  },
  dsn: 'https://<EMAIL>/2'
};

// @ts-ignore schema
export const store = new Store({ schema, defaults }) as unknown as Record<string, any>;

// console.log(store.get('foo'));
// //=> 50

// store.set('foo', '1');
// [Error: Config schema violatio

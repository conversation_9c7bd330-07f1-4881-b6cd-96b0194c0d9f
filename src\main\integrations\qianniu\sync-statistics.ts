import BurrowAdapter from './burrow-adapter';
import got from '../../utils/got';

async function getStatistics(ctx, shop) {
  const adapter = new BurrowAdapter('http://127.0.0.1:22233');
  const res = await adapter.getCs();
  const cs = res.filter((cs) => cs.includes(shop));
  if (!cs.length) {
    return [];
  }
  return [cs[0], await adapter.getStatistics(cs)];
}

async function uploadStatistics(ctx, shop, cs, statistics, aiAgentConfigId) {
  const path = `/message/api/qianniu/saveQianNiuCustomerStatisticsDate`;
  const res = await got
    .post(ctx.env.host + path, {
      json: {
        csName: cs,
        mallId: shop,
        qiuNiuCustomerStatistics: statistics,
        aiAgentConfigId
      },
      headers: {
        authorization: ctx.env.accessToken
      }
    })
    .json();
  ctx.log.info('同步千牛数据成功', res);
}

async function syncStatistics(ctx, shop, aiAgentConfigId) {
  const [cs, statistics] = await getStatistics(ctx, shop);
  if (!cs) {
    ctx.log.warn('无法取到客服，不同步数据');
    return;
  }
  await uploadStatistics(ctx, shop, cs, statistics, aiAgentConfigId);
}

export { syncStatistics };

import { resolve } from 'path';
import { defineConfig, externalizeDepsPlugin } from 'electron-vite';
import { bytecodePlugin } from 'vite-plugin-bytecode2';
import react from '@vitejs/plugin-react';
import { visualizer } from 'rollup-plugin-visualizer';

export default defineConfig({
  main: {
    plugins: [
      externalizeDepsPlugin({
        exclude: [
          'electron-store',
          'p-queue',
          'unique-random-array',
          'p-timeout',
          'got',
          '@sindresorhus/is',
          'download',
          'puppeteer-core',
          'delay'
        ]
      }),
      bytecodePlugin({
        protectedStrings: [
          'AKID9C3lJfoqgKl02PhngNtGNHzXwAD5nNm2',
          'FLG0a2JROEtOitujNqAIG3f7SYqwrbBZ',
          'LTAI5tFBv4yNY7ecjJDi88tg',
          '******************************'
        ]
        // removeBundleJS: false
      }),
      visualizer({
        // open: true,
      })
    ],
    build: {
      rollupOptions: {
        external: ['../../../resources/native_modules/sqlite3/lib/sqlite3']
      }
    }
  },
  preload: {
    plugins: [externalizeDepsPlugin()],
    build: {
      rollupOptions: {
        input: {
          toolbar: resolve('src/preload/toolbar.ts'),
          view: resolve('src/preload/view.ts'),
          defaultView: resolve('src/preload/default-view.ts')
        }
      }
    }
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src')
      }
    },
    plugins: [react()],
    build: {
      rollupOptions: {
        input: {
          toolbar: resolve('src/renderer/toolbar/index.html'),
          defaultView: resolve('src/renderer/default-view/index.html'),
          qianniu: resolve('src/renderer/add-ons/qianniu/index.html'),
          qianniu_reply: resolve('src/renderer/add-ons/qianniu_reply/index.html'),
          qianniu_details: resolve('src/renderer/add-ons/qianniu_details/index.html')
        }
      }
    }
  }
});

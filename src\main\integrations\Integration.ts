import { EventEmitter } from 'node:events';
import { IntegrationConfig } from './index';
import { getLogger, clientId } from '../log';
import path from 'node:path';
import { app, WebContentsView } from 'electron';
import fs from 'fs-extra';
import { Page } from 'puppeteer-core';
import { createPromiseCapability } from '../utils/promise';
import { pptr } from '../pptr';
import { v4 as uuidv4 } from 'uuid';
import Tab from '../tab';

export abstract class Integration extends EventEmitter {
  private page: Promise<Page> | undefined;
  private log: ReturnType<typeof getLogger>;
  private exposeRegistry = new Map();
  protected clientId = clientId;

  constructor(
    protected tab: Tab,
    protected config: IntegrationConfig
  ) {
    super();
    // 连接 DevTools Protocol，版本可以根据需要调整
    this.tab.view.webContents.debugger.attach('1.3');
    this.initDebuggerOnMessage();
  }

  protected getLogger() {
    if (!this.log) {
      this.log = getLogger(this.config.id);
    }

    return this.log;
  }

  protected getCacheDir(): string {
    const dir = path.join(app.getPath('temp'), 'integrations', this.config.id);
    fs.ensureDirSync(dir);
    return dir;
  }

  protected async runCodeInView(code: string, userGesture?: boolean): Promise<any> {
    return this.tab.view.webContents.executeJavaScript(code, userGesture);
  }

  protected async getPptrPage(): Promise<Page> {
    if (!this.page) {
      this.getLogger().debug('init pptr page');
      const capability = createPromiseCapability<Page>();
      this.page = capability.promise;

      if (this.tab.isLoading) {
        this.tab.view.webContents.once('did-finish-load', () => {
          capability.resolve(pptr.getPage(this.tab.view));
        });
      } else {
        capability.resolve(pptr.getPage(this.tab.view));
      }
    }
    return this.page;
  }

  protected sendMessageToOpener(channel: string, data: any) {
    if (!this.tab.opener?.integration) {
      this.getLogger().warn("opener's integration doesn't exist");
      return;
    }
    this.tab.opener?.integration.emit(channel, data);
  }

  // TODO order private and protected
  private async initDebuggerOnMessage() {
    await this.tab.view.webContents.debugger.sendCommand('Runtime.enable');
    this.tab.view.webContents.debugger.on('message', async (_, method, params) => {
      // 监听请求事件
      if (method === 'Network.requestWillBeSent') {
        this.emit('request', params.request, params);
      }
      // 监听响应事件
      if (method === 'Network.responseReceived') {
        const response = {
          ...params.response,
          json: async (): Promise<unknown> => {
            return new Promise((resolve, reject) => {
              try {
                this.getResponseBody(params.requestId).then((res) => {
                  if (typeof res.body === 'string' && res.body.trim()) {
                    resolve(JSON.parse(res.body));
                  } else {
                    reject(new Error('Invalid JSON response'));
                  }
                });
              } catch (e) {
                reject(e);
              }
            });
          }
        };
        this.emit('response', response, params);
      }
      if (method === 'Runtime.consoleAPICalled') {
        const { type, args } = params;
        const [functionId, ...argumentInfo] = args;
        const callbackFn = this.exposeRegistry.get(functionId.value);
        if (type === 'log' && functionId && callbackFn) {
          const callbackArgs = await this.restoreStructure(this.tab.view.webContents, argumentInfo);
          Promise.resolve(callbackFn(...callbackArgs)).catch((err) => {
            this.getLogger().debug(`${functionId.value}执行失败`, err);
          });
        }
      }
    });
  }

  protected async getResponseBody(requestId): Promise<any> {
    return await this.tab.view.webContents.debugger.sendCommand('Network.getResponseBody', {
      requestId
    });
  }

  protected async useDebuggerOnRequestAndResponse(): Promise<void> {
    // 启用网络事件监听
    this.tab.view.webContents.debugger.sendCommand('Network.enable');
  }

  protected async exposeFunction(name, fn): Promise<any> {
    if (typeof name !== 'string' || typeof fn !== 'function') {
      throw new Error('Invalid arguments: exposeFunction expects a string and a function.');
    }
    const uniqueId = uuidv4(); // 生成唯一 ID
    this.exposeRegistry.set(uniqueId, fn);
    // 注入自定义的 JS 函数到页面
    return this.tab.view.webContents.debugger.sendCommand('Runtime.evaluate', {
      expression: `
    (function() {
      window['${name}'] = function(...args) {
        console.log('${uniqueId}', ...args);
      };
    })();
  `
    });
  }

  protected evaluate(fn, params?: any): Promise<any> {
    // 将参数序列化为字符串
    const paramsSerialized = this.serializeParams(params);
    // 将传入的函数字符串化，并在执行时使用反序列化的参数
    let expression = '';
    if (Array.isArray(params) || (typeof params === 'object' && params !== null)) {
      expression = `
      (function() {
        const args = JSON.parse(${paramsSerialized});
        return (${fn.toString()}).apply(null, [args]);
      })();`;
    } else {
      expression = `
    (function() {
      const args = ${paramsSerialized};
      return (${fn.toString()}).apply(null, [args]);
    })();`;
    }

    return this.tab.view.webContents.debugger
      .sendCommand('Runtime.evaluate', {
        expression: expression,
        returnByValue: true,
        awaitPromise: true
      })
      .then((response) => {
        // 取出返回的对象
        const result = response.result.value;
        return result;
      })
      .catch((error) => {
        return error;
      });
  }

  private async restoreStructure(webContents, data): Promise<any> {
    const restored: any[] = [];

    for (const item of data) {
      if (item.subtype === 'array') {
        restored.push(await this.getArgumentsFromUpdateAgentStatue(webContents, item.objectId));
      } else if (item.type === 'object') {
        // 处理对象
        if (item.objectId && item.preview && item.preview.properties) {
          const obj = {};
          for (const prop of item.preview.properties) {
            obj[prop.name] = this.convertToProperType(prop); // 直接获取预览中的属性值
          }
          restored.push(obj);
        }
      } else if (item.type === 'number' || item.type === 'string' || item.type === 'boolean') {
        // 处理数字
        restored.push(this.convertToProperType(item));
      } else if (item.type === 'function') {
        // 处理函数，将函数描述转化为实际可执行函数
        restored.push(new Function(`return ${item.description}`)());
      }
    }

    return restored;
  }

  private async getValueFromObjectId(webContents, objectId): Promise<any> {
    const properties = await webContents.debugger.sendCommand('Runtime.getProperties', {
      objectId
    });

    // 检查是否为数组（通过检测是否有 `length` 属性）
    if (properties.result.some((p) => p.name === 'length' && p.isOwn)) {
      const array: any[] = [];
      for (const prop of properties.result) {
        // 只处理自身的数组项（数字索引），忽略 'length' 等非索引属性
        if (prop.name.match(/^\d+$/) && prop.isOwn) {
          if (prop.value && prop.value.objectId) {
            array.push(await this.getValueFromObjectId(webContents, prop.value.objectId));
          } else if (prop.value) {
            // 处理基础类型值
            array.push(this.convertToProperType(prop));
          }
        }
      }
      return array;
    } else {
      // 普通对象处理
      const result = {};
      for (const prop of properties.result) {
        if (prop.isOwn && prop.name !== 'length') {
          // 忽略 `length` 属性
          if (prop.value && prop.value.objectId) {
            result[prop.name] = await this.getValueFromObjectId(webContents, prop.value.objectId);
          } else if (prop.value) {
            result[prop.name] = this.convertToProperType(prop);
          }
        }
      }
      return result;
    }
  }

  // 新增的函数，用于将不同类型值转换为对应的基础类型
  private convertToProperType(propValue): any {
    switch (propValue.type) {
      case 'boolean':
        return propValue.value === 'true';
      case 'number':
        return Number(propValue.value);
      case 'string':
        return propValue.value;
      case 'undefined':
        return undefined;
      case 'null':
        return null;
      default:
        return propValue.description || propValue.value;
    }
  }

  private async getArgumentsFromUpdateAgentStatue(webContents, objectId): Promise<any> {
    const properties = await webContents.debugger.sendCommand('Runtime.getProperties', {
      objectId
    });
    const args: any[] = [];

    // 处理每个参数
    for (const prop of properties.result) {
      if (prop.isOwn) {
        if (prop.value && prop.value.objectId) {
          // 递归处理对象或数组
          const argValue = await this.getValueFromObjectId(webContents, prop.value.objectId);
          args.push(argValue);
        } else if (prop.value) {
          // 处理基础类型值
          args.push(this.convertToProperType(prop.value));
        }
      }
    }
    return args;
  }

  // 工具函数：根据参数类型处理序列化和重建函数
  private serializeParams(params: any): any {
    if (typeof params === 'function') {
      // 如果参数是函数，直接将其转换为字符串
      return params.toString();
    } else if (typeof params === 'number' || typeof params === 'boolean') {
      // 如果参数是基础数据类型（数字、布尔），直接返回它的值
      return String(params);
    } else if (typeof params === 'string') {
      // 如果参数是字符串，返回带有引号的字符串
      return `'${params}'`;
    } else if (Array.isArray(params)) {
      // 如果参数是数组，递归处理每一个元素
      return `[${params.map(this.serializeParams).join(', ')}]`;
    } else if (typeof params === 'object' && params !== null) {
      // 如果参数是对象，使用 JSON 序列化
      return JSON.stringify(params);
    } else if (params === null) {
      // 处理 null 值
      return 'null';
    } else if (typeof params === 'undefined') {
      // 处理 undefined
      return 'undefined';
    } else {
      throw new Error(`Unsupported parameter type: ${typeof params}`);
    }
  }

  abstract start(): Promise<void>;
  abstract stop(): Promise<void>;
}

import React from 'react';
import {
  InfoCircleOutlined,
  UserOutlined,
  LeftOutlined,
  RightOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { Button, Input, Tooltip } from 'antd';

const App: React.FC<{
  url: string;
  onUrlChange: (string) => void;
  canGoBack: boolean;
  canGoForward: boolean;
  goBack: () => void;
  goForward: () => void;
  reload: () => void;
}> = ({ url, onUrlChange, canGoBack, canGoForward, goBack, goForward, reload }) => (
  <div className="address-bar">
    <Button icon={<LeftOutlined />} disabled={!canGoBack} onClick={goBack} />
    <Button icon={<RightOutlined />} disabled={!canGoForward} onClick={goForward} />
    <Button icon={<ReloadOutlined />} onClick={reload} />
    <Input
      placeholder="Enter your username"
      prefix={<UserOutlined style={{ color: 'rgba(0,0,0,.25)' }} />}
      suffix={
        <Tooltip title="Extra information">
          <InfoCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }} />
        </Tooltip>
      }
      onPressEnter={(e) => {
        const target = e.target as HTMLInputElement;
        window.xBrowser.changeUrl(target.value);
      }}
      value={url}
      onChange={(e) => {
        onUrlChange(e.target.value);
      }}
    />
  </div>
);

export default App;

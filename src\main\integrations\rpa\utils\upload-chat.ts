import got from '../../../utils/got';
import { ReportMessage } from '../type';

export async function upload(data, ctx) {
  // const endpoint = 'http://100.64.0.7:3001/chat';
  const endpoint = 'https://msg.in.dongchacat.cn/chat';
  try {
    await got.post(endpoint, {
      json: data,
      headers: {
        authorization: `iamasi<PERSON><PERSON>h`
      }
    });
    ctx.log.info('对话上传成功', data.user);
  } catch (e: any) {
    ctx.log.error('对话上传失败', data.user, e);
  }
}

export async function upload2MyQA(data: ReportMessage[], ctx) {
  const endpoint = ctx.env.endpoint + '/workbench/chat_logs';

  try {
    await got.post(endpoint, {
      json: {
        loglist: data
      }
    });
    ctx.log.info('对话上传MyQA成功');
  } catch (e: any) {
    ctx.log.error('对话上传MyQA失败', JSON.stringify(data), e);
  }
}

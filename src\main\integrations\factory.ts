import { WebContentsView } from 'electron';
import { log } from '../log';
import { IntegrationConfig } from './index';
import <PERSON><PERSON><PERSON> from './qianniu';
import Doudian from './doudian';
import DoudianPuppeteer from './doudian_puppeteer';
import { Integration } from './Integration';
import Home from './home';
import Tab from '../tab';

/**
 * 插件可能有2种情况
 * 1. 运行在node中，可以使用各种nodeapi,称为integration
 * 2. 运行在web中,称为extension
 */

export function create(tab: Tab, config: IntegrationConfig) {
  switch (config.name) {
    case 'qianniu':
      return new <PERSON><PERSON><PERSON>(tab, config);
    case 'doudian':
      return new <PERSON>udian(tab, config);
    case 'doudian_puppeteer':
      return new DoudianPuppeteer(tab, config);
    case 'home':
      return new Home(tab, config);
    default:
      log.warn('Unsupported integration', config);
      return null;
  }
}

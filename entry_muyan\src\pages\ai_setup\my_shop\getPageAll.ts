/* eslint-disable @typescript-eslint/no-explicit-any */
// 导出一个异步方法getPageAll，该方法负责通过分页请求数据，并使用回调函数处理结果，最终返回所有分页数据。

/**
 * @function getPageAll
 * @description 递归获取所有分页数据的方法。它接受指定的分页大小、当前页码、HTTP 请求方法、处理单个页面响应数据的回调函数以及可选的额外参数。默认情况下，newData 初始化为空数组，agm 参数为可选的附加查询参数对象。
 *
 * @param {number} page_size - 每页数据的数量。
 * @param {number} page_num - 当前请求的页码（从1开始）。
 * @param {Function} http - HTTP 请求函数，接收分页参数和额外参数，返回Promise，解析后得到单个页面的数据。
 * @param {Function} callback - 处理单个页面响应数据的回调函数，接收响应数据作为参数，并返回需要合并到总数据集的新数组。
 * @param {any[]} newData - 用于存储累计获取的所有数据，默认为空数组。
 * @param {any} [agm] - 可选的附加查询参数对象，若不传入则默认赋值为一个空对象。
 *
 * @returns {Promise<any[]>} - 返回一个Promise，解析后得到所有分页数据合并后的数组。
 */
export async function getPageAll(
  page_size: number,
  page_num: number,
  http,
  callback: (res: any) => any[], // 回调函数，用于处理单个页面的数据
  newData: any[] = [],
  agm?: any
): Promise<any[]> {
  // 如果agm参数未传入，则赋予一个空对象
  const agmo = agm ?? {};
  // 发起HTTP请求并处理响应数据
  await http({ page_size, page_num, ...agmo }).then(async (res) => {
    // 将处理后的当前页面数据追加到newData数组中
    newData.push(...callback(res));

    // 若当前页面数据的数量等于或大于每页大小，则继续请求下一页数据
    if (callback(res).length >= page_size) {
      await getPageAll(page_size, page_num + 1, http, callback, newData);
    }
  });

  // 返回累积的所有分页数据
  return newData;
}
